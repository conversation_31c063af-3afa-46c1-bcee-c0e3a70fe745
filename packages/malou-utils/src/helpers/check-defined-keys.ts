import { isNil } from 'lodash';

export type NonNullableKeys<T, K extends keyof T> = T & {
    [P in K]-?: NonNullable<T[P]>;
};

export type NonNullableAllKeys<T> = {
    [P in keyof T]-?: NonNullable<T[P]>;
};

export function filterByRequiredKeys<T, K extends keyof T>(items: T[], requiredKeys: K[]): NonNullableKeys<T, K>[] {
    return items.filter((item): item is NonNullableKeys<T, K> =>
        requiredKeys.every((key) => item[key] !== null && item[key] !== undefined)
    );
}

export function ensureAllKeysAreDefined<T extends object | undefined | null>(obj: T): NonNullableAllKeys<T> {
    if (isNil(obj)) {
        throw new Error('Object is null or undefined');
    }

    if (Object.keys(obj).length === 0) {
        throw new Error('Object has no keys');
    }

    Object.entries(obj).forEach(([key, value]) => {
        if (isNil(value)) {
            throw new Error(`Property ${key} is null or undefined`);
        }
    });

    return obj as NonNullableAllKeys<T>; // Return the object with all keys non-nullable
}
