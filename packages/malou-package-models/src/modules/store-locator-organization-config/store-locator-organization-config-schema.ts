import {
    cleanUrl,
    isValidLanguageCodeISO_1,
    isValidUrl,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorCentralizationPageElementIds,
    StoreLocatorCommonElementIds,
    StoreLocatorFaqBlockQuestionType,
    StoreLocatorLanguage,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const storeLocatorOrganizationConfigJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        organizationId: {
            type: 'string',
            format: 'objectId',
        },
        cloudfrontDistributionId: {
            type: 'string',
        },
        baseUrl: {
            type: 'string',
            format: 'uri',
            validate: {
                validator: (v) => isValidUrl(v),
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
        },
        languages: {
            $ref: '#/definitions/Languages',
        },
        isLive: {
            type: 'boolean',
            default: false,
            description: 'Indicates if the store locator is still in development mode',
        },
        shouldDisplayWhiteMark: {
            type: 'boolean',
            default: true,
        },
        styles: {
            $ref: '#/definitions/Styles',
        },
        plugins: {
            $ref: '#/definitions/Plugins',
        },
        aiSettings: {
            $ref: '#/definitions/AiSettings',
        },
        blocksSettings: {
            $ref: '#/definitions/BlocksSettings',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: [
        '_id',
        'createdAt',
        'updatedAt',
        'organizationId',
        'cloudfrontDistributionId',
        'baseUrl',
        'languages',
        'blocksSettings',
        'isLive',
        'shouldDisplayWhiteMark',
        'styles',
        'aiSettings',
    ],
    definitions: {
        Styles: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fonts: {
                    $ref: '#/definitions/Fonts',
                },
                colors: {
                    $ref: '#/definitions/Colors',
                },
                pages: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        store: {
                            $ref: '#/definitions/StorePageStyles',
                        },
                        map: {
                            $ref: '#/definitions/MapStyles',
                        },
                        common: {
                            $ref: '#/definitions/CommonStyles',
                        },
                        storeDraft: {
                            $ref: '#/definitions/StorePageStyles',
                        },
                        mapDraft: {
                            $ref: '#/definitions/MapStyles',
                        },
                    },
                    required: ['store', 'map', 'common', 'storeDraft', 'mapDraft'],
                },
            },
            required: ['fonts', 'colors', 'pages'],
            title: 'Styles',
        },
        Fonts: {
            type: 'array',
            items: {
                type: 'object',
                additionalProperties: false,
                properties: {
                    class: {
                        type: 'string',
                        enum: [
                            'primary',
                            'primary-bold',
                            'secondary',
                            'secondary-bold',
                            'tertiary',
                            'tertiary-bold',
                            'fourth',
                            'fourth-bold',
                            'fifth',
                            'fifth-bold',
                            'sixth',
                            'sixth-bold',
                            'seventh',
                            'seventh-bold',
                            'eighth',
                            'eighth-bold',
                            'ninth',
                            'ninth-bold',
                            'tenth',
                            'tenth-bold',
                        ],
                    },
                    src: {
                        type: 'string',
                        format: 'uri',
                        validate: {
                            validator: (v) => isValidUrl(v),
                            message: (props) => `Url should be valid, value: ${props.value}`,
                        },
                    },
                    weight: {
                        type: 'string',
                        enum: ['400', '700', '900'],
                    },
                    style: {
                        type: 'string',
                        enum: ['normal', 'italic'],
                    },
                },
                required: ['class', 'src'],
            },
            title: 'Fonts',
        },
        Colors: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    class: {
                        type: 'string',
                        enum: ['primary', 'secondary', 'tertiary', 'fourth', 'fifth', 'sixth', 'seventh', 'eighth', 'ninth', 'tenth'],
                    },
                    value: {
                        type: 'string',
                        validate: {
                            validator: (v) => /^#?([0-9A-F]{3,4}|[0-9A-F]{6}|[0-9A-F]{8})$/i.test(v),
                            message: (props) => `Color should be in hexa format, value: ${props.value}`,
                        },
                    },
                },
                required: ['class', 'value'],
            },
            title: 'Colors',
        },
        StorePageStyles: {
            type: 'object',
            additionalProperties: true,
            properties: {},
            validate: {
                validator: (v) => {
                    const allowedElementIds = Object.values(StoreLocatorRestaurantPageElementIds);
                    return Object.keys(v).every((key) => allowedElementIds.includes(key as StoreLocatorRestaurantPageElementIds));
                },
                message: (props) => `Styles should only contain valid Page HTML element IDs, value: ${props.value}`,
            },
            title: 'StorePageStyles',
        },
        MapStyles: {
            type: 'object',
            additionalProperties: true,
            properties: {},
            validate: {
                validator: (v) => {
                    const allowedElementIds = Object.values(StoreLocatorCentralizationPageElementIds);
                    return Object.keys(v).every((key) => allowedElementIds.includes(key as StoreLocatorCentralizationPageElementIds));
                },
                message: (props) => `Styles should only contain valid Map HTML element IDs, value: ${props.value}`,
            },
            title: 'MapStyles',
        },
        CommonStyles: {
            type: 'object',
            additionalProperties: true,
            properties: {},
            validate: {
                validator: (v) => {
                    const allowedElementIds = Object.values(StoreLocatorCommonElementIds);
                    return Object.keys(v).every((key) => allowedElementIds.includes(key as StoreLocatorCommonElementIds));
                },
                message: (props) => `Styles should only contain valid Common HTML element IDs, value: ${props.value}`,
            },
            title: 'CommonStyles',
        },
        Plugins: {
            type: 'object',
            additionalProperties: false,
            properties: {
                googleAnalytics: {
                    $ref: '#/definitions/GoogleAnalyticsPlugin',
                },
            },
            title: 'Plugins',
        },
        GoogleAnalyticsPlugin: {
            type: 'object',
            additionalProperties: false,
            properties: {
                trackingId: {
                    type: 'string',
                },
            },
            required: ['trackingId'],
            title: 'GoogleAnalyticsPlugin',
        },
        AiSettings: {
            type: 'object',
            additionalProperties: false,
            properties: {
                tone: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    default: [],
                },
                languageStyle: {
                    enum: Object.values(StoreLocatorAiSettingsLanguageStyle),
                },
                attributeIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                },
                restaurantKeywordIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                },
                specialAttributes: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/SpecialAttributes',
                    },
                },
            },
            required: ['tone', 'languageStyle', 'attributeIds', 'restaurantKeywordIds', 'specialAttributes'],
            default: {
                tone: [],
                languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
                categoryIds: [],
                restaurantKeywordIds: [],
                specialAttributes: [],
            },
            title: 'AiSettings',
        },
        SpecialAttributes: {
            type: 'object',
            additionalProperties: false,
            properties: {
                restaurantId: {
                    type: 'string',
                    format: 'objectId',
                },
                text: {
                    type: 'string',
                },
            },
            required: ['restaurantId', 'text'],
            title: 'SpecialAttributes',
        },
        Languages: {
            type: 'object',
            additionalProperties: false,
            properties: {
                primary: {
                    type: 'string',
                    enum: Object.values(StoreLocatorLanguage),
                    validate: {
                        validator: (v) => isValidLanguageCodeISO_1(v),
                        message: (props) =>
                            `Primary language code must be a valid ISO 639-1 code defined in LanguageCodeISO_1 enum, value: ${props.value}`,
                    },
                    default: StoreLocatorLanguage.UNDETERMINED,
                },
                secondary: {
                    type: 'array',
                    items: {
                        type: 'string',
                        validate: {
                            validator: (v) => isValidLanguageCodeISO_1(v),
                            message: (props) =>
                                `Secondary language code must be a valid ISO 639-1 code defined in LanguageCodeISO_1 enum, value: ${props.value}`,
                        },
                        enum: Object.values(StoreLocatorLanguage),
                    },
                    default: [],
                    description: 'List of secondary language codes',
                },
            },
            required: ['primary', 'secondary'],
            description:
                'Languages used to generate stores. The primary language is required. All codes must be valid values from LanguageCodeISO_1 enum.',
            title: 'Languages',
        },
        BlocksSettings: {
            type: 'object',
            additionalProperties: false,
            properties: {
                faq: {
                    $ref: '#/definitions/FAQsBlockSettings',
                },
                reviews: {
                    $ref: '#/definitions/ReviewsBlockSettings',
                },
            },
            required: ['faq', 'reviews'],
            title: 'BlocksSettings',
        },
        FAQsBlockSettings: {
            type: 'object',
            additionalProperties: false,
            properties: {
                questionsTemplate: {
                    type: 'array',
                    items: {
                        type: 'string',
                        enum: Object.values(StoreLocatorFaqBlockQuestionType),
                    },
                    default: [],
                },
            },
            required: ['questionsTemplate'],
            title: 'FAQsBlockSettings',
        },
        ReviewsBlockSettings: {
            type: 'object',
            additionalProperties: false,
            properties: {
                isOnlyFiveStarsReviews: {
                    type: 'boolean',
                    default: true,
                },
            },
            required: ['isOnlyFiveStarsReviews'],
            title: 'ReviewsBlockSettings',
        },
    },
    title: 'StoreLocatorOrganizationConfig',
} as const satisfies JSONSchemaExtraProps;
