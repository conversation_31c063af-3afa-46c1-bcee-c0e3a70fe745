import { App } from '@slack/bolt';
import { singleton } from 'tsyringe';

import { isNotNil } from '@malou-io/package-utils';

import { Config } from ':config';
import { AsyncLocalStorageService } from ':helpers/classes/async-local-storage-service';
import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';
import {
    generateCloudWatchUrl,
    generateCloudWatchUrlForParentTraceId,
    generateCloudWatchUrlForRestaurantId,
    generateCloudWatchUrlForTraceId,
    generateCloudWatchUrlForUserId,
} from ':plugins/aws';

interface SlackMessagePayload {
    text: string;
    channel: SlackChannel;
    shouldPing?: true | SlackUsers[]; // will ping the corresponding users or all the code owners if true
    /**
     * If present, the message will be sent as an answer in a thread
     * A threadTs can be returned from the sendMessage function
     */
    threadTs?: string;
}

interface ChannelConfiguration {
    sandbox: string;
    prod: string;
    codeOwners: SlackUsers[];
}

interface SlackContextPayload {
    restaurantId?: string;
    userId?: string;
}

interface SlackCommonTextPayload extends SlackContextPayload {
    err?: Error | MalouError;
    endpoint?: string;
    metadata?: any;
}

// --- The bot must be added to the channels --- //
export enum SlackChannel {
    APP_ALERTS = 'APP_ALERTS',
    STORE_LOCATOR_ALERTS = 'STORE_LOCATOR_ALERTS',
    PLATFORM_UPDATES_ALERTS = 'PLATFORM_UPDATES_ALERTS',
    POSTS_V2_ALERTS = 'POSTS_V2_ALERTS',
    POSTS_V1_ALERTS = 'POSTS_V1_ALERTS',
    EXPERIMENTATION_ALERTS = 'EXPERIMENTATION_ALERTS',
    MALOUPE_ALERTS = 'MALOUPE_ALERTS',
    INFRA_ALERTS = 'INFRA_ALERTS',
    REVIEWS_ALERTS = 'REVIEWS_ALERTS',
    CRM_ALERTS = 'CRM_ALERTS',
}

export enum SlackUsers {
    CYRIL = 'U0429N9R3KJ',
    MAXIME = 'U06EQ5KLRPU',
    BAPTISTE = 'U05AHG5PGH5',
    MAZIGH = 'U01197RR1C6',
    VICTOR = 'UQU0904MC',
    TANGUY = 'U03E4TYDQLD',
    HAMZA = 'U06FCUFU0SC',
}

const slackConfiguration: Record<SlackChannel, ChannelConfiguration> = {
    [SlackChannel.APP_ALERTS]: {
        sandbox: 'C07NZQ2FZTR',
        prod: 'C07Q29T5Z5F',
        codeOwners: [SlackUsers.MAXIME, SlackUsers.CYRIL],
    },
    [SlackChannel.STORE_LOCATOR_ALERTS]: {
        sandbox: 'C090ECZ0JTD',
        prod: 'C090EDM7XJA',
        codeOwners: [SlackUsers.MAXIME],
    },
    [SlackChannel.PLATFORM_UPDATES_ALERTS]: {
        sandbox: 'C090NEX0YAY',
        prod: 'C090K6YM7M3',
        codeOwners: [SlackUsers.MAXIME, SlackUsers.TANGUY],
    },
    [SlackChannel.POSTS_V1_ALERTS]: {
        sandbox: 'C090W98N161',
        prod: 'C090RA7BSMR',
        codeOwners: [SlackUsers.CYRIL, SlackUsers.BAPTISTE],
    },
    [SlackChannel.POSTS_V2_ALERTS]: {
        sandbox: 'C08KBGZ4NE9',
        prod: 'C08CPDRA6KX',
        codeOwners: [SlackUsers.CYRIL, SlackUsers.BAPTISTE],
    },
    [SlackChannel.EXPERIMENTATION_ALERTS]: {
        sandbox: 'G016T1JEJ5A',
        prod: 'G016T1JEJ5A',
        codeOwners: [SlackUsers.BAPTISTE],
    },
    [SlackChannel.MALOUPE_ALERTS]: {
        sandbox: 'C090SLDK1U4',
        prod: 'C090JGMS9K8',
        codeOwners: [SlackUsers.TANGUY, SlackUsers.HAMZA],
    },
    [SlackChannel.INFRA_ALERTS]: {
        sandbox: 'C0916NCSDEG',
        prod: 'C090QBXBQ5V',
        codeOwners: [SlackUsers.MAXIME, SlackUsers.CYRIL, SlackUsers.VICTOR],
    },
    [SlackChannel.REVIEWS_ALERTS]: {
        sandbox: 'C090QGQFFMM',
        prod: 'C091JCX61GR',
        codeOwners: [SlackUsers.VICTOR],
    },
    [SlackChannel.CRM_ALERTS]: {
        sandbox: 'C095XG46T8U',
        prod: 'C0969K6GN3B',
        codeOwners: [SlackUsers.BAPTISTE, SlackUsers.CYRIL],
    },
};

const RESOLVE_BUTTON_ID = 'alerts_resolve_button';

@singleton()
export class SlackService {
    private readonly _app: App | null = null;

    constructor(
        private readonly _asyncLocalStorageService: AsyncLocalStorageService,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _usersRepository: UsersRepository
    ) {
        if (['production', 'staging', 'development'].includes(Config.env) && Config.iAmA !== 'developer') {
            this._app = new App({
                token: Config.services.slack.techBot?.token,
                signingSecret: Config.services.slack.techBot?.signingSecret,
            });
        }
    }

    sendMessage({ channel, text, threadTs, shouldPing }: SlackMessagePayload): void {
        void this._sendMessageAsync({ channel, text, threadTs, shouldPing }).catch((err) =>
            logger.error('[SLACK SERVICE] Failed to send message', {
                channel,
                text,
                err,
            })
        );
    }

    sendAlert<T = Parameters<typeof this._createSlackAlertMessage>[0]>({
        data,
        template,
        channel,
        shouldPing,
    }: {
        data: T;
        template?: (payload: T) => Promise<{ mainMessage: string; extraMessages: string[] }>;
        channel: SlackChannel;
        shouldPing?: true | SlackUsers[];
    }): void {
        void this._sendAlertAsync({ data, template, channel, shouldPing }).catch((err) =>
            logger.error('[SLACK SERVICE] Failed to send alert', {
                data,
                channel,
                err,
            })
        );
    }

    private async _sendMessageAsync({ channel, text, threadTs, shouldPing }: SlackMessagePayload): Promise<string | undefined> {
        if (!this._app) {
            logger.warn('[SLACK SERVICE] Bypass sending message because bot is not initialized', {
                channel,
                text,
            });
            return;
        }
        const channelId = Config.env === 'production' ? slackConfiguration[channel].prod : slackConfiguration[channel].sandbox;

        const extraContext = this._getExtraContext({ threadTs, shouldPing, channel });
        text += extraContext;

        const response = await this._app.client.chat.postMessage({
            channel: channelId,
            text,
            ...(threadTs && { thread_ts: threadTs }),
            ...(!threadTs && {
                blocks: [
                    {
                        type: 'section',
                        text: {
                            type: 'mrkdwn',
                            text: text,
                        },
                    },
                    {
                        type: 'actions',
                        elements: [
                            {
                                type: 'button',
                                text: {
                                    type: 'plain_text',
                                    text: ':white_check_mark: Resolve',
                                    emoji: true,
                                },
                                action_id: RESOLVE_BUTTON_ID,
                                style: 'primary',
                            },
                        ],
                    },
                ],
            }),
        });

        if (!response.ok) {
            throw new Error(`Slack API error: ${response.error}`);
        }

        logger.info('[SLACK SERVICE] Message sent', {
            channel,
            text,
        });

        return response.ts;
    }

    /**
     * Adds information such as the environment and user pings (e.g., @Tanguy, @Hamza, @Victor).
     */
    private _getExtraContext({
        threadTs,
        shouldPing,
        channel,
    }: {
        threadTs?: string;
        shouldPing?: true | SlackUsers[];
        channel: SlackChannel;
    }): string {
        // Add environment only in non-production environments, and main messages
        let extraContext = '';
        if (threadTs) {
            return extraContext; // No extra context for thread messages
        }

        if (Config.env !== 'production') {
            extraContext += `♻️ *Environment:* ${Config.env}`;
        }

        if (isNotNil(shouldPing) && Config.env === 'production') {
            const usersToPing = shouldPing === true ? slackConfiguration[channel].codeOwners : shouldPing;
            if (usersToPing && usersToPing.length > 0) {
                const userMentions = usersToPing.map((user) => `<@${user}>`).join(' ');
                extraContext += extraContext ? '\n' : '';
                extraContext += `🕵️ ${userMentions}`;
            }
        }

        extraContext = extraContext ? `\n\n${extraContext}` : '';
        return extraContext;
    }

    private async _sendAlertAsync<T = Parameters<typeof this._createSlackAlertMessage>[0]>({
        data,
        template,
        channel,
        shouldPing,
    }: {
        data: T;
        template?: (payload: T) => Promise<{ mainMessage: string; extraMessages: string[] }>;
        channel: SlackChannel;
        shouldPing?: true | SlackUsers[];
    }): Promise<void> {
        if (!this._app || !data) {
            logger.warn('[SLACK SERVICE] Bypass sending message because bot is not initialized', {
                channel,
                data,
            });
            return;
        }

        const computedTemplate = template ?? this._createSlackAlertMessage.bind(this);
        const { mainMessage, extraMessages } = await computedTemplate(data);

        const threadTs = await this._sendMessageAsync({
            channel,
            text: mainMessage,
            shouldPing,
        });

        if (threadTs && extraMessages?.length > 0) {
            for (const extraMessage of extraMessages) {
                await this._sendMessageAsync({
                    channel,
                    text: extraMessage,
                    threadTs,
                }).catch((err) => {
                    logger.error('[SLACK SERVICE] Failed to send extra message', {
                        extraMessage,
                        channel,
                        threadTs,
                        err,
                    });
                });
            }
        }
    }

    private async _createSlackAlertMessage(payload?: SlackCommonTextPayload): Promise<{ mainMessage: string; extraMessages: string[] }> {
        let extraContext = await this.createContextForSlack(payload);
        const metadata = {
            ...(payload?.metadata ?? {}),
            ...(this._asyncLocalStorageService.getStore()?.metadata ?? {}),
        };

        let errorData = '';
        let extraMetadata = '';

        const err = payload?.err;
        if (err) {
            errorData += `\n*Error*`;
            errorData += `\n> :love_letter: *Message:* <${generateCloudWatchUrl({ messageFilter: err.message })}|${err.message}>`;

            if (err instanceof MalouError) {
                if (err.message !== err.malouErrorCode) {
                    // eslint-disable-next-line max-len
                    errorData += `\n> :currency_exchange: *MalouErrorCode:* <${generateCloudWatchUrl({ messageFilter: err.malouErrorCode })}|${err.malouErrorCode}>`;
                }

                if (err.metadata && Object.keys(err.metadata).length > 0) {
                    extraMetadata += `\n:bar_chart: *Metadata:* \`\`\`${JSON.stringify(err.metadata)}\`\`\``;
                }
            }

            if (err.stack) {
                extraMetadata += `\n:mag_right: *Stacktrace:* \`\`\`${err.stack}\`\`\``;
            }
        }

        if (metadata && Object.keys(metadata).length > 0) {
            extraMetadata += `\n:bar_chart: *Extra metadata* \`\`\`${JSON.stringify(metadata)}\`\`\``;
        }

        const endpoint = payload?.endpoint;
        if (endpoint) {
            extraContext += `\n> :link: *Endpoint:* <${Config.baseAppUrl}/${endpoint}|Endpoint>`;
        }

        const title = `:red_circle: *NEW ALERT* :red_circle:`;
        return { mainMessage: `${title}${errorData}${extraContext}`, extraMessages: [extraMetadata] };
    }

    async createContextForSlack(payload?: SlackContextPayload): Promise<string> {
        const restaurantId = payload?.restaurantId ?? this._asyncLocalStorageService.getRestaurantId();
        const userId = payload?.userId ?? this._asyncLocalStorageService.getUserId();
        const traceId = this._asyncLocalStorageService.getTraceId();
        const parentTraceId = this._asyncLocalStorageService.getParentTraceId();

        const [restaurant, user] = await Promise.all([
            restaurantId ? this._restaurantsRepository.getRestaurantById(restaurantId) : Promise.resolve(undefined),
            userId ? this._usersRepository.findById(userId) : Promise.resolve(undefined),
        ]);

        let extraContext = `\n`;
        let extraLinks = '';
        if (isNotNil(restaurant)) {
            const restaurantLink = `<${Config.baseAppUrl}/restaurants/${restaurant._id.toString()}|${restaurant.name}>`;
            const logsLink = `<${generateCloudWatchUrlForRestaurantId(restaurant._id.toString())}|Logs>`;
            extraContext += `\n> :knife_fork_plate: *Restaurant:* ${restaurantLink} · ${logsLink}`;
        }

        if (isNotNil(user)) {
            const logsLink = userId ? `<${generateCloudWatchUrlForUserId(userId)}|Logs>` : '';
            extraContext += `\n> :face_holding_back_tears: *User:* ${user.name} (${user.email}) · ${logsLink}`;
        }

        if (traceId) {
            extraLinks = `\n> :eyes: *Logs:* <${generateCloudWatchUrlForTraceId(traceId)}|Trace>`;
        }

        if (parentTraceId) {
            extraLinks = `\n> :eyes: *Logs:* <${generateCloudWatchUrlForParentTraceId(parentTraceId)}|Parent trace>`;
        }

        return `${extraContext}${extraLinks}`;
    }
}
