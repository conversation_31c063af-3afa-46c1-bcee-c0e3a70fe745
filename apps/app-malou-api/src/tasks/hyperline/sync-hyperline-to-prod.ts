/* Use this task as a playground to run stuff using any app logic.
You can also just copy paste to another file to keep track of previous run tasks */
import 'reflect-metadata';

import ':env';

import ':di';
import { container, singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { BusinessCategory } from '@malou-io/package-utils';

import OrganizationsRepository from ':modules/organizations/organizations.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import ':plugins/db';
import { HyperlineApiCustomer } from ':providers/hyperline/hyperline.interfaces';

@singleton()
class DefaultTask {
    constructor(
        private readonly _subscriptionsProvider: SubscriptionsProvider,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _organizationsRepository: OrganizationsRepository
    ) {}

    async execute() {
        const HYPERLINE_RETURNED_COUNT = 50;
        let skip = 0;
        let allHyperlineCustomers: HyperlineApiCustomer[] = [];
        let hyperlineCustomers: HyperlineApiCustomer[] = [];
        do {
            hyperlineCustomers = await this._subscriptionsProvider.getCustomers(skip);
            for (const customer of hyperlineCustomers) {
                if (!customer.external_id) {
                    continue;
                }
                const organization = await this._organizationsRepository.findOne({
                    filter: { _id: toDbId(customer.external_id) },
                    options: { lean: true },
                });
                if (organization) {
                    await this._organizationsRepository.updateOne({
                        filter: { _id: toDbId(customer.external_id) },
                        update: { subscriptionsProviderId: customer.id },
                    });
                    continue;
                }
                const restaurant = await this._restaurantsRepository.findOne({
                    filter: { _id: toDbId(customer.external_id) },
                    options: { lean: true },
                });
                if (restaurant) {
                    await this._restaurantsRepository.updateOne({
                        filter: { _id: toDbId(customer.external_id) },
                        update: { subscriptionsProviderId: customer.id },
                    });

                    if (restaurant.type === BusinessCategory.BRAND && customer.custom_properties.brand_account !== true) {
                        console.log('Brand account not found for brand', restaurant.name, restaurant._id, customer);
                    }
                    if (
                        restaurant.type === BusinessCategory.LOCAL_BUSINESS &&
                        customer.custom_properties.google_place_id !== restaurant.placeId
                    ) {
                        console.log('Place ID mismatch for local business', restaurant.name, restaurant._id, customer);
                    }
                    continue;
                }
            }
            skip += HYPERLINE_RETURNED_COUNT;
        } while (hyperlineCustomers.length > 0);
        console.log('allHyperlineCustomers :>> ', allHyperlineCustomers.length);
    }
}

const task = container.resolve(DefaultTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
