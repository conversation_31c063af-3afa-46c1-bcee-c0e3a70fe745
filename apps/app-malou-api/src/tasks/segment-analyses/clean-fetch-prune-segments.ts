import 'reflect-metadata';

import ':env';

import { chunk } from 'lodash';
import { DateTime } from 'luxon';
import { container, singleton } from 'tsyringe';

import { DbId, IRestaurant, toDbId, toDbIds } from '@malou-io/package-models';
import { SemanticAnalysisFetchStatus } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { ReviewSemanticAnalysisProducer } from ':modules/segment-analyses/queues/review-semantic-analysis/review-semantic-analysis.producer';
import { SegmentAnalysesRepository } from ':modules/segment-analyses/segment-analyses.repository';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';
import ':plugins/db';

@singleton()
class CleanFetchPruneSegmentsTask {
    private readonly _REVIEW_CHUNK_SIZE = 100;
    private readonly _SEMANTIC_ANALYSIS_START_DATE = new Date('2025-01-01T00:00:00.000Z');
    private readonly _MAX_MONTHS_TO_FETCH = 9;

    constructor(
        private readonly _segmentAnalysesRepository: SegmentAnalysesRepository,
        private readonly _segmentAnalysesParentTopicsRepository: SegmentAnalysisParentTopicsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _reviewSemanticAnalysisProducer: ReviewSemanticAnalysisProducer
    ) {
        this._reviewSemanticAnalysisProducer.initialize();
    }

    async execute() {
        const restaurantIds = ['67f67f0ae15a4fa3f6aaf059'];
        const restaurants = await this._restaurantsRepository.find({
            filter: { _id: { $in: toDbIds(restaurantIds) } },
            projection: { _id: 1, name: 1 },
            options: { lean: true },
        });
        if (!restaurants || restaurants.length === 0) {
            console.log(`Restaurants not found`);
            return;
        }

        for (const restaurant of restaurants) {
            console.log(`Processing restaurant: ${restaurant.name}`);
            await this._processRestaurant(restaurant._id.toString());
        }
    }

    private async _processRestaurant(restaurantId: string): Promise<void> {
        await this._cleanSegmentAnalyses(restaurantId);
        for (let month = 0; month <= this._MAX_MONTHS_TO_FETCH; month++) {
            const monthDate = DateTime.fromJSDate(this._SEMANTIC_ANALYSIS_START_DATE).plus({ months: month });
            await this._fetchOneMonthOfSemanticAnalysisForRestaurant(restaurantId, monthDate);
            await this._startPruningSegments(restaurantId, monthDate);
        }
    }

    private async _cleanSegmentAnalyses(restaurantId: string): Promise<void> {
        const platforms = await this._platformsRepository.find({
            filter: { restaurantId: toDbId(restaurantId) },
            projection: { socialId: 1, key: 1 },
            options: { lean: true },
        });

        const platformSocialIds = platforms.map((platform) => platform.socialId).filter(Boolean);

        // Delete segment analyses
        const deletedSegmentAnalysesResult = await this._segmentAnalysesRepository.deleteMany({
            filter: { platformSocialId: { $in: platformSocialIds } },
        });
        logger.info(`Deleted segmentAnalyses : ${deletedSegmentAnalysesResult.deletedCount}`);

        // Delete segment analyses parent topics
        const deletedSegmentAnalysisParentTopicResult = await this._segmentAnalysesParentTopicsRepository.deleteMany({
            filter: { restaurantId: toDbId(restaurantId) },
        });
        logger.info(`Deleted segmentAnalysisParentTopics : ${deletedSegmentAnalysisParentTopicResult.deletedCount}`);

        // Update reviews status
        const updatedReviewsResult = await this._reviewsRepository.updateMany({
            filter: { restaurantId: toDbId(restaurantId), semanticAnalysisFetchStatus: { $ne: null } },
            update: { semanticAnalysisFetchStatus: null },
            options: { lean: true },
        });
        logger.info(`Updated reviews : ${updatedReviewsResult.modifiedCount}`);
    }

    private async _fetchOneMonthOfSemanticAnalysisForRestaurant(restaurantId: string, monthDate: DateTime): Promise<void> {
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { _id: toDbId(restaurantId) },
            projection: { _id: 1, name: 1 },
        });
        if (!restaurant) {
            console.log(`Restaurant with id ${restaurantId} not found`);
            return;
        }
        console.log(`Will update semantic analysis for restaurant ${restaurant.name}`);

        const filter = {
            restaurantId: restaurant._id,
            text: { $ne: null },
            socialCreatedAt: {
                $gte: monthDate.toJSDate(),
                $lt: monthDate.endOf('month').toJSDate(),
            },
        };
        const options: { lean: boolean; sort: { socialCreatedAt: 1 | -1 } } = {
            lean: true,
            sort: { socialCreatedAt: 1 }, // Sort older to newer
        };

        await this._fetchSemanticAnalysisForReviews(restaurant, filter, options);
        await this._fetchSemanticAnalysisForPrivateReviews(restaurant, filter, options);
    }

    private async _fetchSemanticAnalysisForReviews(restaurant: Pick<IRestaurant, '_id' | 'name'>, filter, options): Promise<void> {
        const reviews = await this._reviewsRepository.find({
            filter,
            projection: { _id: 1 },
            options,
        });
        const reviewChunks = chunk(reviews, this._REVIEW_CHUNK_SIZE);
        for (const reviewChunk of reviewChunks) {
            try {
                await Promise.all(
                    reviewChunk.map((review) => this._fetchSemanticAnalysisForSingleReview(review._id, restaurant._id, false))
                );
                console.log(`Finished ${reviewChunk.length} reviews...`);
            } catch (err) {
                console.log(`Error for restaurant ${restaurant._id}`);
            }
        }
    }

    private async _fetchSemanticAnalysisForPrivateReviews(restaurant: Pick<IRestaurant, '_id' | 'name'>, filter, options): Promise<void> {
        const privateReviews = await this._privateReviewsRepository.find({
            filter,
            projection: { _id: 1 },
            options,
        });
        const privateReviewChunks = chunk(privateReviews, this._REVIEW_CHUNK_SIZE);
        for (const privateReviewChunk of privateReviewChunks) {
            try {
                await Promise.all(
                    privateReviewChunk.map((privateReview) =>
                        this._fetchSemanticAnalysisForSingleReview(privateReview._id, restaurant._id, true)
                    )
                );
                console.log(`Finished ${privateReviewChunk.length} private reviews...`);
            } catch (err) {
                console.log(`Error for restaurant ${restaurant._id}`);
            }
        }
    }

    private async _fetchSemanticAnalysisForSingleReview(reviewId: DbId, restaurantId: DbId, isPrivateReview: boolean): Promise<void> {
        await this._reviewSemanticAnalysisProducer.execute({
            reviewId: reviewId.toString(),
            restaurantId: restaurantId.toString(),
            isPrivateReview,
        });
        const repository: any = isPrivateReview ? this._privateReviewsRepository : this._reviewsRepository;
        await repository.findOneAndUpdate({
            filter: { _id: reviewId },
            update: { semanticAnalysisFetchStatus: SemanticAnalysisFetchStatus.PENDING },
        });
    }

    private async _startPruningSegments(restaurantId: string, pruningDate: DateTime): Promise<void> {
        await this._reviewSemanticAnalysisProducer.execute({
            reviewId: '', // Not used in this context, but required by the producer
            restaurantId: restaurantId.toString(),
            isPrivateReview: false,
            pruningDate: pruningDate.endOf('month').toJSDate(),
        });
    }
}

const task = container.resolve(CleanFetchPruneSegmentsTask);
task.execute()
    .then(() => {
        process.exit(0);
    })
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
