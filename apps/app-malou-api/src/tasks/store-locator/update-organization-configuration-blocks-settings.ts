import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { StoreLocatorOrganizationConfigRepository } from ':modules/store-locator/store-locator-organization-config.repository';

@singleton()
class UpdateOrganizationConfigurationBlocksSettings {
    constructor(private readonly _storeLocatorOrganizationConfigRepository: StoreLocatorOrganizationConfigRepository) {}

    async execute(): Promise<void> {
        const organizationConfigsWithoutBlocksSettings = await this._storeLocatorOrganizationConfigRepository.find({
            filter: {
                blocksSettings: { $exists: false },
            },
        });

        console.log('Found', organizationConfigsWithoutBlocksSettings.length, 'organization configurations without blocksSettings');

        if (organizationConfigsWithoutBlocksSettings.length === 0) {
            console.log('No organization configurations to update');
            return;
        }

        let index = 0;
        for (const orgConfig of organizationConfigsWithoutBlocksSettings) {
            console.log(
                'Updating organization configuration with ID:',
                orgConfig._id,
                `(${++index}/${organizationConfigsWithoutBlocksSettings.length})`
            );
            await this._storeLocatorOrganizationConfigRepository.findOneAndUpdate({
                filter: { _id: orgConfig._id },
                update: {
                    $set: {
                        blocksSettings: {
                            reviews: {
                                isOnlyFiveStarsReviews: true,
                            },
                            faq: {
                                questionsTemplate: [],
                            },
                        },
                    },
                },
            });
        }
    }
}

const task = container.resolve(UpdateOrganizationConfigurationBlocksSettings);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
