import { ICategory, IRestaurant, OverwriteOrAssign } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    AiModel,
    AiModelProvider,
    AiPostGenerationEmojiStatus,
    ApplicationLanguage,
    CustomerNaming,
    FrenchTutoiementVouvoiement,
    IntelligentSubjectName,
    ReviewAnalysisSentiment,
    ReviewAnalysisSubCategory,
    ReviewAnalysisTag,
    StoreLocatorLanguage,
} from '@malou-io/package-utils';

import { AIResponseStyle, AiReviewerNameValidation } from ':microservices/ai-previous-review-analysis.service';
import { AiReviewAnalysisCategory, AiReviewAnalysisSubCategory } from ':microservices/ai-semantic-analysis.service';

export interface CreateCompletionResponse {
    promptTokenCount?: number;
    completionText: string;
    completionTokenCount?: number;
    responseTimeInMilliseconds: number;
    completionTimeInMilliseconds: number;
}

export type RestaurantWithCategory = OverwriteOrAssign<
    IRestaurant,
    {
        category: ICategory;
    }
>;

export namespace AiPayloadOptions {
    export const defaultReviewerName = 'a reviewer';
    export const defaultBusinessCategoryName = 'business';

    export const previousReviewsSampleSize = 50;
    export const previousPostsSampleSize = 5;
}

export interface GenerateMediaAnalysisPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.MEDIA;
    type: AiInteractionType.GENERATE_IMAGE_DESCRIPTION;
    restaurantData: {
        imageLink: string;
    };
}

export enum GenerateMediaDescriptionImageType {
    ALT_TEXT_INFORMATION_BLOCK = 'alt_text_information_block',
    ALT_TEXT_GALLERY_BLOCK = 'alt_text_gallery_block',
    ALT_TEXT_SOCIAL_MEDIA_POST = 'alt_text_social_media_post',
    ALT_TEXT_DESCRIPTIONS_BLOCK = 'alt_text_descriptions_block',
}

export interface GenerateMediaDescriptionPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.STORE_LOCATOR_RESTAURANT_PAGE;
    type: AiInteractionType.ALTERNATIVE_TEXT;
    restaurantData: {
        imageType: GenerateMediaDescriptionImageType;
        imageLink: string;
        language: StoreLocatorLanguage;
        keywords: string[];
        restaurantName: string;
    };
}

export interface GenerateReviewRelevantBricksPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS;
    type: AiInteractionType.REVIEW_RELATED_BRICKS_DETECTION;
    restaurantData: {
        reviewText: string;
        reviewRating: number;
        reviewLanguage: string;
        bricks: {
            category: string;
            text: string;
            translationsId?: string;
            translations: {
                en: string;
                es: string;
                it: string;
                fr: string;
            };
        }[];
    };
}

export interface GenerateReviewReplyAdvancedSettingsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS;
    type: AiInteractionType.ANSWER_REVIEW_ADVANCED_SETTINGS;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        previousReviewsComments: { reviewText: string; responseText: string }[];
        platform: string;
        reviewRating?: number | null;
        reviewerName: string;
        review: string;
        badwords: string[];
        signature: string;
        shouldTranslateSignature: boolean;
        userAddress: CustomerNaming;
        tone: FrenchTutoiementVouvoiement;
        introductiveSentence: string;
        shouldTranslateIntroductiveSentence: boolean;
        customPrompt: string;
        responseStyle: AIResponseStyle;
        reviewerNameValidation: AiReviewerNameValidation;
    };
    model: AiModel;
    modelProvider: AiModelProvider;
}
export interface OptimizeReviewReplyAdvancedSettingsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS;
    type: AiInteractionType.OPTIMIZE_REVIEW_ANSWER_ADVANCED_SETTINGS;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        previousReviewsComments: { reviewText: string; responseText: string }[];
        textToOptimize: string;
        badwords: string[];
        signature: string;
        shouldTranslateSignature: boolean;
        userAddress: CustomerNaming;
        tone: FrenchTutoiementVouvoiement;
        introductiveSentence: string;
        shouldTranslateIntroductiveSentence: boolean;
        platform: string;
        reviewRating?: number | null;
        reviewerName: string;
        responseStyle: AIResponseStyle;
        reviewerNameValidation: AiReviewerNameValidation;
    };
    model: string;
    modelProvider: string;
}

export interface KeywordBreakdownPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.KEYWORDS;
    type: AiInteractionType.BREAKDOWN_AND_CLASSIFY_KEYWORDS;
    restaurantData: {
        keywords: string[];
    };
}

export interface GenerateSocialNetworkPostTextPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.GENERATE_SOCIAL_NETWORK_POST;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        previousPosts: string[];
        keywords: string[];
        description: string;
    };
}

export interface GenerateSocialNetworkPostTextAdvancedSettingsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.GENERATE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        previousPosts: string[];
        keywords: string[];
        description: string;
        length: string; // should be a number as string
        tone: string[];
        containsEmojis: AiPostGenerationEmojiStatus;
        photoDescription: string;
    };
}

export interface OptimizeSocialNetworkPostTextPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.OPTIMIZE_SOCIAL_NETWORK_POST;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        keywords: string[];
        textToOptimize: string;
    };
}

export interface OptimizeSocialNetworkPostTextAdvancedSettingsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.OPTIMIZE_SOCIAL_NETWORK_POST_ADVANCED_SETTINGS;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        keywords: string[];
        textToOptimize: string;
        length: string; // should be a number as string
        tone: string[];
        containsEmojis: AiPostGenerationEmojiStatus;
    };
}

export interface GenerateSeoPostTextPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.GENERATE_SEO_POST;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        description: string;
        previousPosts: string[];
        language: string;
        keywords: string[];
    };
}

export interface GenerateSeoPostTextAdvancedSettingsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.GENERATE_SEO_POST_ADVANCED_SETTINGS;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        description: string;
        previousPosts: string[];
        language: string;
        keywords: string[];
        length: string; // should be a number as string
        tone: string[];
        containsEmojis: AiPostGenerationEmojiStatus;
        photoDescription: string;
        denomination: string;
        openPrompt?: string | null;
    };
}

export interface OptimizeSeoPostTextPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.OPTIMIZE_SEO_POST;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        keywords: string[];
        textToOptimize: string;
    };
}

export interface OptimizeSeoPostTextAdvancedSettingsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.OPTIMIZE_SEO_POST_ADVANCED_SETTINGS;
    restaurantData: {
        restaurantName: string;
        restaurantCategory: string;
        restaurantCity: string;
        language: string;
        keywords: string[];
        textToOptimize: string;
        length: string; // should be a number as string
        tone: string[];
        containsEmojis: AiPostGenerationEmojiStatus;
    };
}

export interface ChoosePostHashtagsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.CHOOSE_POST_HASHTAGS;
    restaurantData: {
        post: string;
        hashtags: string[];
    };
}

export interface TranslateTextPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection;
    type: AiInteractionType;
    restaurantData: {
        text: string;
        language?: string;
        restaurantName?: string;
        introduction?: string;
        signature?: string;
    };
}

export interface DuplicateSeoPostTextPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.GENERATE_SEO_DUPLICATION;
    restaurantData: {
        restaurantName: string;
        restaurantCity: string;
        postalCode?: string;
        formattedAddress: string;
        keywords: string[];
        bricks: string[];
        venueLocationBricks?: string[];
        postDescription: string;
        language: string;
        duplicator: {
            restaurantName: string;
            restaurantCity: string;
            postalCode?: string;
            formattedAddress: string;
            keywords: string[];
            bricks: string[];
        };
    };
}

export interface GeneratePostSettingsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.RESTAURANT_AI_SETTINGS;
    type: AiInteractionType.POST_ADVANCED_SETTINGS_DETECTION;
    restaurantData: {
        previousPosts: string[];
        language: string;
    };
}

export interface DuplicateSocialPostTextPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.POSTS;
    type: AiInteractionType.GENERATE_RS_DUPLICATION;
    restaurantData: {
        restaurantName: string;
        restaurantCity?: string;
        postalCode?: string;
        formattedAddress?: string;
        postDescription?: string;
        language: string;
        duplicator: {
            restaurantName: string;
            restaurantCity?: string;
            postalCode?: string;
            formattedAddress?: string;
        };
    };
}

export interface SingleParentTopicPayload {
    topicId: string;
    title: string;
    sentiment: ReviewAnalysisSentiment | null;
}

export interface CategoryParentTopicsPayload {
    category: ReviewAnalysisTag | ReviewAnalysisSubCategory;
    topics: SingleParentTopicPayload[];
}

export interface GenerateFullReviewSemanticAnalysisPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS;
    type: AiInteractionType.REVIEW_SEMANTIC_ANALYSIS_AND_TOPIC_LINKING | AiInteractionType.REVIEW_SEMANTIC_ANALYSIS_AND_TOPIC_LINKING_V2;
    restaurantData: {
        review: {
            rating: number;
            reviewTitle: string;
            reviewText: string;
            ratingTags: string[];
            menuItems: { text: string; sentiment: ReviewAnalysisSentiment }[];
        };
        topics: CategoryParentTopicsPayload[];
    };
}

// For Maloupe
// Will execute only step one of GenerateFullReviewSemanticAnalysisPayload
export interface GenerateReviewSemanticAnalysisWithoutTopicsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS;
    type: AiInteractionType.REVIEW_SEMANTIC_ANALYSIS;
    restaurantData: {
        rating: number;
        reviewTitle?: string;
        reviewText: string;
    };
}

// Will execute only step two of GenerateFullReviewSemanticAnalysisPayload
export interface GenerateSemanticAnalysisTopicLinkingPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.SEGMENT_ANALYSES;
    type: AiInteractionType.TOPIC_LINKING | AiInteractionType.TOPIC_LINKING_V2;
    restaurantData: {
        items: {
            itemId: string;
            subCategory: AiReviewAnalysisSubCategory | null;
            category: AiReviewAnalysisCategory;
            topic: string;
            sentiment: ReviewAnalysisSentiment;
            verbatim: string;
            isTag: boolean;
        }[];
        topics: CategoryParentTopicsPayload[];
    };
}

export interface LanguageDetectionPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection;
    type: AiInteractionType.LANGUAGE_DETECTION;
    restaurantData: {
        text: string;
    };
}

export interface TextTranslationTask {
    baseLanguage: ApplicationLanguage;
    text: string[];
}

export interface TextTranslationPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection;
    type: AiInteractionType.TRANSLATION;
    restaurantData: {
        tasks: TextTranslationTask[];
        translationLanguages: ApplicationLanguage[];
    };
}

export interface DetectIntelligentSubjectsPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS;
    type: AiInteractionType.INTELLIGENT_SUBJECTS_DETECTION;
    restaurantData: {
        reviewText: string;
        intelligentSubjects: string[];
    };
}

export type DetectIntelligentSubjectsResponse = {
    intelligentSubject: IntelligentSubjectName;
    isDetected: boolean;
    sentiment?: ReviewAnalysisSentiment;
}[];

export type PreviousReviewsAnalysisPayload = {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS;
    type: AiInteractionType.PREVIOUS_REVIEWS_ANALYSIS_AND_REVIEWER_NAME_VALIDATION;
    restaurantData: {
        previousReviewsComments: { [reviewId: string]: { reviewText: string; responseText: string } };
        reviewText: string;
        reviewerName: string;
    };
};

export interface SemanticAnalysisTopicPruningPayload {
    relatedEntityCollection: AiInteractionRelatedEntityCollection.SEGMENT_ANALYSES;
    type: AiInteractionType.TOPIC_PRUNING;
    restaurantData: {
        reviewsWithCommentsCount: number[];
        topics: Record<AiReviewAnalysisCategory, SemanticAnalysisTopicPruningPayloadTopic[]>;
        currentDate: Date;
    };
}

export interface SemanticAnalysisTopicPruningPayloadTopic {
    topicId: string;
    title: string;
    createdAt: Date;
    isAiVisible: boolean;
    isUserInput: boolean;
    isFavorite: boolean;
    lastLinkedAt: Date | null;
    numberOfLinkedReviews: number;
    subCategory?: AiReviewAnalysisSubCategory;
}

export interface SemanticAnalysisTopicPruningResponse {
    topics: {
        topicId: string;
        isAiVisible: boolean;
    }[];
}
