import { singleton } from 'tsyringe';

import { AiInteractionRelatedEntityCollection, AiInteractionType, DEFAULT_REVIEW_RATING } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { GenericAiServiceCompletionResponse } from ':microservices/ai-lambda-template/generic-ai-service';
import {
    AiSegmentAnalysisWithoutTopics,
    AiSemanticAnalysisWithoutTopicsService,
} from ':microservices/ai-semantic-analysis-without-topics.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { GenerateReviewSemanticAnalysisWithoutTopicsPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { ReviewSemanticAnalysisMapper } from ':modules/review-analyses/platforms/openai/mapper';
import { SegmentAnalysis } from ':modules/segment-analyses/entities/segment-analysis.entity';

interface GenerateReviewSemanticAnalysisWithoutTopicsServiceForNonMalouRestaurantInput {
    review: {
        text: string;
        rating?: number;
        title?: string;
        socialId?: string;
    };
    placeId: string;
}

export type SegmentAnalysisWithoutTopic = Pick<SegmentAnalysis, 'category' | 'sentiment' | 'aiFoundSegment' | 'segment'>;

@singleton()
export class GenerateReviewSemanticAnalysisWithoutTopicsService {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _aiSemanticAnalysisWithoutTopicsService: AiSemanticAnalysisWithoutTopicsService,
        private readonly _aiCompletionMapper: AiCompletionMapper
    ) {}

    async execute({
        review,
        placeId,
    }: GenerateReviewSemanticAnalysisWithoutTopicsServiceForNonMalouRestaurantInput): Promise<SegmentAnalysisWithoutTopic[]> {
        const aiInteraction = await this._aiInteractionsRepository.createAiInteraction({
            type: AiInteractionType.REVIEW_SEMANTIC_ANALYSIS,
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            placeId,
        });

        try {
            const payload: GenerateReviewSemanticAnalysisWithoutTopicsPayload = {
                relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
                type: AiInteractionType.REVIEW_SEMANTIC_ANALYSIS,
                restaurantData: {
                    reviewText: review.text,
                    rating: review.rating ?? DEFAULT_REVIEW_RATING,
                    reviewTitle: review.title ?? '',
                },
            };

            const { aiResponse, aiInteractionDetails } = await this._aiSemanticAnalysisWithoutTopicsService.generateAnalysis(payload);

            const mainAiInteractionCompletionIndex = aiInteractionDetails.findIndex(
                (aiInt) => aiInt.type === AiInteractionType.REVIEW_SEMANTIC_ANALYSIS
            );
            const mainAiInteractionCompletion =
                mainAiInteractionCompletionIndex !== -1 ? aiInteractionDetails[mainAiInteractionCompletionIndex] : undefined;
            if (mainAiInteractionCompletionIndex > -1) {
                aiInteractionDetails.splice(mainAiInteractionCompletionIndex, 1);
            }
            if (aiInteractionDetails?.length) {
                await this._saveAdditionalInteractions({
                    placeId,
                    aiInteractions: aiInteractionDetails,
                });
            }

            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(mainAiInteractionCompletion);

            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: updatedAiInteraction,
                options: { new: true },
            });

            return aiResponse?.map((segment: AiSegmentAnalysisWithoutTopics) => ({
                category: ReviewSemanticAnalysisMapper.mapToMalouSegmentCategory(segment.category),
                sentiment: ReviewSemanticAnalysisMapper.mapToMalouSentiment(segment.sentiment),
                aiFoundSegment: segment.verbatim,
                segment:
                    segment.isTag || segment.isMenuItem
                        ? segment.verbatim
                        : (ReviewSemanticAnalysisMapper.cleanSegment(review.text, segment.verbatim) ?? ''),
            }));
        } catch (error: any) {
            logger.error('[AiUseCases] [generateReviewSemanticAnalysisWithoutTopicsService] Error', {
                error: error.stack,
                placeId,
                reviewSocialId: review.socialId,
            });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _saveAdditionalInteractions({
        placeId,
        aiInteractions,
    }: {
        placeId: string;
        aiInteractions: GenericAiServiceCompletionResponse[];
    }): Promise<void> {
        if (!aiInteractions || !aiInteractions.length) {
            return;
        }
        const aiInteractionsToSave = aiInteractions.map((aiInteraction) => ({
            ...this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(aiInteraction),
            placeId,
            userId: undefined,
            relatedEntityId: undefined,
            restaurantId: undefined,
            relatedEntityCollection: aiInteraction.relatedEntityCollection || AiInteractionRelatedEntityCollection.REVIEWS,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
        await this._aiInteractionsRepository.createAiInteractions(aiInteractionsToSave);
    }
}
