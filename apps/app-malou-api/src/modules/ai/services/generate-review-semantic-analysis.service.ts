import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPrivateReview, IReview, ITranslations, toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    DEFAULT_REVIEW_RATING,
    MalouErrorCode,
    PlatformKey,
    ReviewAnalysisSentiment,
    TranslationSource,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GenericAiServiceCompletionResponse } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiSegmentAnalysis, AiSemanticAnalysisService } from ':microservices/ai-semantic-analysis.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers/assert-restaurant-can-make-ai-call.helper';
import { GenerateFullReviewSemanticAnalysisPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewSemanticAnalysisMapper } from ':modules/review-analyses/platforms/openai/mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { SegmentAnalysis } from ':modules/segment-analyses/entities/segment-analysis.entity';
import { GetAllSegmentAnalysisParentTopicsForRestaurantService } from ':modules/segment-analysis-parent-topics/services/get-all-segment-analysis-parent-topics-for-restaurant/get-all-segment-analysis-parent-topics-for-restaurant.service';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

interface GenerateReviewSemanticAnalysisInput {
    relatedEntityId: string;
    restaurantId: string;
    isPrivateReview: boolean;
}

export type SegmentAnalysisNewParentTopic = Omit<ITranslations, '_id' | 'createdAt' | 'updatedAt'> & {
    sentiment?: ReviewAnalysisSentiment | null;
};

export type SegmentAnalysisWithNewTopic = Omit<SegmentAnalysis, 'id' | 'segmentAnalysisParentTopicIds'> & {
    segmentAnalysisParentTopicId?: string;
    segmentNewParentTopic?: SegmentAnalysisNewParentTopic;
    restaurantId: string;
};

@singleton()
export class GenerateReviewSemanticAnalysisService {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiSemanticAnalysisService: AiSemanticAnalysisService,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _getAllSegmentAnalysisParentTopicsForRestaurantService: GetAllSegmentAnalysisParentTopicsForRestaurantService
    ) {}

    async execute({
        relatedEntityId,
        restaurantId,
        isPrivateReview,
    }: GenerateReviewSemanticAnalysisInput): Promise<SegmentAnalysisWithNewTopic[]> {
        const aiInteraction = await this._aiInteractionsRepository.createAiInteraction({
            type: AiInteractionType.REVIEW_SEMANTIC_ANALYSIS,
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            relatedEntityId,
            restaurantId,
        });

        try {
            const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);

            if (!restaurant) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const review: IReview | IPrivateReview | null = isPrivateReview
                ? await this._privateReviewsRepository.getPrivateReviewById(relatedEntityId)
                : await this._reviewsRepository.getReviewById(relatedEntityId);

            if (!review) {
                logger.warn('[AI_USE_CASE] Review not found', { reviewId: relatedEntityId });
                throw new MalouError(MalouErrorCode.REVIEW_NOT_FOUND, {
                    message: 'Review not found',
                    metadata: { reviewId: relatedEntityId },
                });
            }

            const isNewSemanticAnalysisV2FeatureEnabled = await isFeatureAvailableForRestaurant({
                restaurantId: review.restaurantId.toString(),
                featureName: 'release-new-semantic-analysis-v2',
            });

            const payload = await this._computePayload({ review, isPrivateReview, isNewSemanticAnalysisV2FeatureEnabled });

            const { aiResponse, aiInteractionDetails } = await this._aiSemanticAnalysisService.generateAnalysis(payload);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: AiInteractionType.REVIEW_SEMANTIC_ANALYSIS,
            });

            const mainAiInteractionCompletionIndex = aiInteractionDetails.findIndex(
                (aiInt) => aiInt.type === AiInteractionType.REVIEW_SEMANTIC_ANALYSIS
            );
            const mainAiInteractionCompletion =
                mainAiInteractionCompletionIndex !== -1 ? aiInteractionDetails[mainAiInteractionCompletionIndex] : undefined;
            if (mainAiInteractionCompletionIndex > -1) {
                aiInteractionDetails.splice(mainAiInteractionCompletionIndex, 1);
            }
            // type can be REVIEW_SEMANTIC_ANALYSIS or TOPIC_TRANSLATION or TOPIC_LINKING or TOPIC_CREATION or REVIEW_ITEM_DETECTION
            if (aiInteractionDetails?.length) {
                await this._saveAdditionalInteractions({
                    restaurantId,
                    relatedEntityId,
                    aiInteractions: aiInteractionDetails,
                });
            }

            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                mainAiInteractionCompletion,
                restaurant._id
            );

            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: updatedAiInteraction,
                options: { new: true },
            });

            const platformSocialId = await this._getPlatformSocialId({ isPrivateReview, review });

            assert(platformSocialId, 'Platform social id is required');

            return aiResponse?.map((segment: AiSegmentAnalysis) => ({
                platformKey: isPrivateReview ? PlatformKey.PRIVATE : (review.key as PlatformKey),
                reviewSocialId: isPrivateReview ? review._id.toString() : (review as IReview).socialId.toString(),
                reviewSocialCreatedAt: review.socialCreatedAt,
                platformSocialId,
                restaurantId: review.restaurantId.toString(),
                topic: segment.topic,
                category: ReviewSemanticAnalysisMapper.mapToMalouSegmentCategory(segment.category),
                subcategory: segment.subCategory
                    ? ReviewSemanticAnalysisMapper.mapToMalouSegmentSubcategory(segment.subCategory)
                    : undefined,
                sentiment: ReviewSemanticAnalysisMapper.mapToMalouSentiment(segment.sentiment),
                aiFoundSegment: segment.verbatim,
                isRatingTagOrMenuItem: (segment.isTag || segment.isMenuItem) ?? false,
                segment:
                    segment.isTag || segment.isMenuItem
                        ? segment.verbatim
                        : review.text
                          ? (ReviewSemanticAnalysisMapper.cleanSegment(review.text, segment.verbatim) ?? '')
                          : '',
                segmentAnalysisParentTopicId: segment.linkedTopicId ?? undefined,
                segmentNewParentTopic: segment.newTopic
                    ? {
                          language: ApplicationLanguage.EN, // we always take topics in english
                          source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
                          ...segment.newTopic,
                          sentiment: segment.newTopic.sentiment
                              ? ReviewSemanticAnalysisMapper.mapToMalouSentiment(segment.newTopic.sentiment)
                              : null,
                      }
                    : undefined,
            }));
        } catch (error: any) {
            logger.error('[AiUseCases] [generateReviewSemanticAnalysisService] Error', {
                error: error.stack,
                relatedEntityId,
                restaurantId,
            });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _saveAdditionalInteractions({
        restaurantId,
        relatedEntityId,
        aiInteractions,
    }: {
        restaurantId: string;
        relatedEntityId: string;
        aiInteractions: GenericAiServiceCompletionResponse[];
    }): Promise<void> {
        if (!aiInteractions || !aiInteractions.length) {
            return;
        }
        const aiInteractionsToSave = aiInteractions.map((aiInteraction) => ({
            ...this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(aiInteraction, toDbId(restaurantId)),
            restaurantId,
            userId: undefined,
            relatedEntityId,
            relatedEntityCollection: aiInteraction.relatedEntityCollection || AiInteractionRelatedEntityCollection.REVIEWS,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
        await this._aiInteractionsRepository.createAiInteractions(aiInteractionsToSave);
    }

    private async _getPlatformSocialId({
        isPrivateReview,
        review,
    }: {
        isPrivateReview: boolean;
        review: IReview | IPrivateReview;
    }): Promise<string | undefined> {
        if (isPrivateReview) {
            return review.restaurantId.toString();
        }
        const platform = await this._platformsRepository.findOne({
            filter: { _id: toDbId((review as IReview).platformId) },
            projection: { socialId: 1 },
            options: { lean: true },
        });
        return platform?.socialId ?? undefined;
    }

    private async _computePayload({
        review,
        isPrivateReview,
        isNewSemanticAnalysisV2FeatureEnabled,
    }: {
        review: IReview | IPrivateReview;
        isPrivateReview: boolean;
        isNewSemanticAnalysisV2FeatureEnabled: boolean;
    }): Promise<GenerateFullReviewSemanticAnalysisPayload> {
        const topics = await this._getAllSegmentAnalysisParentTopicsForRestaurantService.execute({
            restaurantId: review.restaurantId.toString(),
            isNewSemanticAnalysisV2FeatureEnabled,
        });

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.REVIEWS,
            type: isNewSemanticAnalysisV2FeatureEnabled
                ? AiInteractionType.REVIEW_SEMANTIC_ANALYSIS_AND_TOPIC_LINKING_V2
                : AiInteractionType.REVIEW_SEMANTIC_ANALYSIS_AND_TOPIC_LINKING,
            restaurantData: {
                review: {
                    reviewText: review.text ?? '',
                    rating: review.rating ?? DEFAULT_REVIEW_RATING,
                    reviewTitle: isPrivateReview ? '' : ((review as IReview).title ?? ''),
                    ratingTags: isPrivateReview ? [] : ((review as IReview).ratingTags ?? []),
                    menuItems: isPrivateReview
                        ? []
                        : ((review as IReview).menuItemReviews?.map((menuItem) => ({
                              text: menuItem.name,
                              sentiment: menuItem.rating ? ReviewAnalysisSentiment.POSITIVE : ReviewAnalysisSentiment.NEGATIVE,
                          })) ?? []),
                },
                topics,
            },
        };
    }
}
