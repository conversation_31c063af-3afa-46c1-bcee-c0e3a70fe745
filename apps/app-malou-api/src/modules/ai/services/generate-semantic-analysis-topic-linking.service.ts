import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import {
    AI_HARD_LIMIT_CALL_COUNT,
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    ApplicationLanguage,
    isNotNil,
    MalouErrorCode,
    TranslationSource,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { GenericAiServiceCompletionResponse } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiSemanticAnalysisTopicLinkingService } from ':microservices/ai-semantic-analysis-topic-linking.service';
import { AiSegmentAnalysis } from ':microservices/ai-semantic-analysis.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { assertRestaurantCanMakeAiCall } from ':modules/ai/helpers/assert-restaurant-can-make-ai-call.helper';
import { GenerateSemanticAnalysisTopicLinkingPayload } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { SegmentAnalysisWithNewTopic } from ':modules/ai/services/generate-review-semantic-analysis.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewSemanticAnalysisMapper } from ':modules/review-analyses/platforms/openai/mapper';
import { SegmentAnalysis } from ':modules/segment-analyses/entities/segment-analysis.entity';
import { GetAllSegmentAnalysisParentTopicsForRestaurantService } from ':modules/segment-analysis-parent-topics/services/get-all-segment-analysis-parent-topics-for-restaurant/get-all-segment-analysis-parent-topics-for-restaurant.service';
import { isFeatureAvailableForRestaurant } from ':services/experimentations-service/experimentation.service';

interface GenerateSemanticAnalysisTopicLinkingInput {
    restaurantId: string;
    segments: SegmentAnalysis[];
}

// Will only execute only step two of generate-review-semantic-analysis service
@singleton()
export class GenerateSemanticAnalysisTopicLinkingService {
    constructor(
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _aiSemanticAnalysisTopicLinkingService: AiSemanticAnalysisTopicLinkingService,
        private readonly _aiCompletionMapper: AiCompletionMapper,
        private readonly _getAllSegmentAnalysisParentTopicsForRestaurantService: GetAllSegmentAnalysisParentTopicsForRestaurantService
    ) {}

    async execute({ restaurantId, segments }: GenerateSemanticAnalysisTopicLinkingInput): Promise<SegmentAnalysisWithNewTopic[]> {
        const isNewSemanticAnalysisV2FeatureEnabled = await isFeatureAvailableForRestaurant({
            restaurantId: restaurantId.toString(),
            featureName: 'release-new-semantic-analysis-v2',
        });

        const aiInteractionType = isNewSemanticAnalysisV2FeatureEnabled
            ? AiInteractionType.TOPIC_LINKING_V2
            : AiInteractionType.TOPIC_LINKING;

        const aiInteraction = await this._aiInteractionsRepository.createAiInteraction({
            type: aiInteractionType,
            relatedEntityCollection: AiInteractionRelatedEntityCollection.SEGMENT_ANALYSES,
            restaurantId,
        });

        try {
            const restaurant = await this._restaurantsRepository.getRestaurantById(restaurantId);

            if (!restaurant) {
                logger.warn('[AI_USE_CASE] Restaurant not found', { restaurantId });
                throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'Restaurant not found', metadata: { restaurantId } });
            }

            assertRestaurantCanMakeAiCall(restaurant, AI_HARD_LIMIT_CALL_COUNT);

            const payload = await this._computePayload({ restaurantId, segments, isNewSemanticAnalysisV2FeatureEnabled });

            const { aiResponse, aiInteractionDetails } = await this._aiSemanticAnalysisTopicLinkingService.generateMapping(payload);

            await this._restaurantsRepository.incrementRestaurantAiCallCount({
                restaurantId: restaurant._id,
                feature: aiInteractionType,
            });

            const mainAiInteractionCompletionIndex = aiInteractionDetails.findIndex((aiInt) => aiInt.type === aiInteractionType);
            const mainAiInteractionCompletion =
                mainAiInteractionCompletionIndex !== -1 ? aiInteractionDetails[mainAiInteractionCompletionIndex] : undefined;
            if (mainAiInteractionCompletionIndex > -1) {
                aiInteractionDetails.splice(mainAiInteractionCompletionIndex, 1);
            }
            // type can be REVIEW_SEMANTIC_ANALYSIS or TOPIC_TRANSLATION or TOPIC_LINKING or TOPIC_CREATION or REVIEW_ITEM_DETECTION
            if (aiInteractionDetails.length) {
                await this._saveAdditionalInteractions({
                    restaurantId,
                    aiInteractions: aiInteractionDetails,
                });
            }

            const updatedAiInteraction = this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(
                mainAiInteractionCompletion,
                restaurant._id
            );

            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: updatedAiInteraction,
                options: { new: true },
            });

            return aiResponse
                ?.map((segment: AiSegmentAnalysis) => {
                    const associatedSegment = segments.find(
                        (s) =>
                            s.segment === segment.verbatim &&
                            s.topic === segment.topic &&
                            s.sentiment === segment.sentiment &&
                            s.category === ReviewSemanticAnalysisMapper.mapToMalouSegmentCategory(segment.category) &&
                            s.subcategory === ReviewSemanticAnalysisMapper.mapToMalouSegmentSubcategory(segment.subCategory)
                    );
                    if (!associatedSegment) {
                        return null;
                    }
                    return {
                        platformKey: associatedSegment.platformKey,
                        reviewSocialId: associatedSegment.reviewSocialId,
                        reviewSocialCreatedAt: associatedSegment.reviewSocialCreatedAt,
                        platformSocialId: associatedSegment.platformSocialId,
                        topic: segment.topic,
                        restaurantId,
                        category: ReviewSemanticAnalysisMapper.mapToMalouSegmentCategory(segment.category),
                        subcategory: segment.subCategory
                            ? ReviewSemanticAnalysisMapper.mapToMalouSegmentSubcategory(segment.subCategory)
                            : undefined,
                        sentiment: ReviewSemanticAnalysisMapper.mapToMalouSentiment(segment.sentiment),
                        aiFoundSegment: segment.verbatim,
                        isRatingTagOrMenuItem: (segment.isTag || segment.isMenuItem) ?? false,
                        segment:
                            segment.isTag || segment.isMenuItem
                                ? segment.verbatim
                                : (ReviewSemanticAnalysisMapper.cleanSegment(associatedSegment.segment, segment.verbatim) ?? ''),
                        segmentAnalysisParentTopicId: segment.linkedTopicId ?? undefined,
                        segmentNewParentTopic: segment.newTopic
                            ? {
                                  language: ApplicationLanguage.EN, // we always take topics in english
                                  source: TranslationSource.SERVERLESS_AI_TEXT_GENERATOR,
                                  ...segment.newTopic,
                                  sentiment: segment.newTopic.sentiment
                                      ? ReviewSemanticAnalysisMapper.mapToMalouSentiment(segment.newTopic.sentiment)
                                      : null,
                              }
                            : undefined,
                    };
                })
                .filter(isNotNil);
        } catch (error: any) {
            logger.error('[AiUseCases] [generateSemanticAnalysisTopicLinkingService] Error', {
                error: error.stack,
                restaurantId,
            });
            await this._aiInteractionsRepository.findOneAndUpdate({
                filter: { _id: aiInteraction.id },
                update: {
                    error: {
                        malouErrorCode: error?.malouErrorCode,
                        message: error?.message,
                        stack: error?.stack,
                    },
                },
            });
            throw error;
        }
    }

    private async _saveAdditionalInteractions({
        restaurantId,
        aiInteractions,
    }: {
        restaurantId: string;
        aiInteractions: GenericAiServiceCompletionResponse[];
    }): Promise<void> {
        if (!aiInteractions || !aiInteractions.length) {
            return;
        }
        const aiInteractionsToSave = aiInteractions.map((aiInteraction) => ({
            ...this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(aiInteraction, toDbId(restaurantId)),
            restaurantId,
            userId: undefined,
            relatedEntityId: undefined,
            relatedEntityCollection: aiInteraction.relatedEntityCollection || AiInteractionRelatedEntityCollection.SEGMENT_ANALYSES,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
        await this._aiInteractionsRepository.createAiInteractions(aiInteractionsToSave);
    }

    private async _computePayload({
        restaurantId,
        segments,
        isNewSemanticAnalysisV2FeatureEnabled,
    }: {
        restaurantId: string;
        segments: SegmentAnalysis[];
        isNewSemanticAnalysisV2FeatureEnabled: boolean;
    }): Promise<GenerateSemanticAnalysisTopicLinkingPayload> {
        const topics = await this._getAllSegmentAnalysisParentTopicsForRestaurantService.execute({
            restaurantId,
            isNewSemanticAnalysisV2FeatureEnabled,
        });

        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.SEGMENT_ANALYSES,
            type: isNewSemanticAnalysisV2FeatureEnabled ? AiInteractionType.TOPIC_LINKING_V2 : AiInteractionType.TOPIC_LINKING,
            restaurantData: {
                items: segments.map((segment) => ({
                    itemId: segment.id,
                    subCategory: ReviewSemanticAnalysisMapper.mapToLambdaSegmentSubCategory(segment.subcategory ?? null),
                    category: ReviewSemanticAnalysisMapper.mapToLambdaSegmentCategory(segment.category),
                    topic: segment.topic,
                    sentiment: segment.sentiment,
                    verbatim: segment.segment,
                    isTag: segment.isRatingTagOrMenuItem ?? false,
                })),
                topics,
            },
        };
    }
}
