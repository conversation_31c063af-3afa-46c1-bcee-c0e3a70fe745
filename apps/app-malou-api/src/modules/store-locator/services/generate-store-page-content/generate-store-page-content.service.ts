import { uniq } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { GenerateStoreLocatorStorePageContentBodyDto } from '@malou-io/package-dto';
import { IStoreLocatorRestaurantPage, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import { GenerateStoreLocatorContentType, MalouErrorCode, RestaurantAttributeValue, StoreLocatorLanguage } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import {
    AiStoreLocatorContentService,
    AiStoreLocatorContentType,
    AiStoreLocatorDescriptionsContent,
    AiStoreLocatorFaqContent,
    GenerateStoreLocatorContentPayload,
} from ':microservices/ai-store-locator-content-generator.service';
import { RestaurantAttributesRepository } from ':modules/restaurant-attributes/restaurant-attributes.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { StoreLocatorOrganizationConfiguration } from ':modules/store-locator/entities/store-locator.organization-configuration.entity';
import { GetRestaurantOffersService } from ':modules/store-locator/services/get-restaurant-offers/get-restaurant-offers.service';

interface WholeStorePageContent {
    pageUrl: string;
    title: string;
    metaDescription: string;
    twitterDescription: string;
    descriptions: AiStoreLocatorDescriptionsContent['blocks'];
    ctaTitle: string;
    reviewsTitle: string;
    galleryTitle: string;
    gallerySubtitle: string;
    socialNetworksTitle: string;
    faqs: AiStoreLocatorFaqContent['faqs'];
}

@singleton()
export class GenerateStorePageContentService {
    constructor(
        private readonly _aiStoreLocatorContentService: AiStoreLocatorContentService,
        private readonly _getRestaurantOffersService: GetRestaurantOffersService,
        private readonly _restaurantAttributesRepository: RestaurantAttributesRepository,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async generateWholePageContent({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<WholeStorePageContent> {
        const payload = await this._getLambdaPayload({
            restaurantId,
            storeLocatorOrganizationConfig,
            lang,
        });

        const generateFaqBlockPayload: GenerateStoreLocatorContentPayload['restaurantData'] = {
            ...payload,
            questionsTemplate: storeLocatorOrganizationConfig.blocksSettings.faq.questionsTemplate,
        };

        const [
            { text: pageUrl },
            { text: title },
            { text: twitterDescription },
            { blocks: descriptions },
            { text: ctaTitle },
            { text: reviewsTitle },
            { faqs },
        ] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.RESTAURANT_PAGE_URL_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.H1_TITLE_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.HEAD_META_TWITTER_DESCRIPTION_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION,
                payload,
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.FAQ_BLOCK_GENERATION,
                payload: generateFaqBlockPayload,
            }),
        ]);

        const [{ text: metaDescription }, { text: galleryTitle }, { text: socialNetworksTitle }] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: title }],
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: title }],
                },
            }),
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION]: reviewsTitle }],
                },
            }),
        ]);

        const [{ text: gallerySubtitle }] = await Promise.all([
            this.generateStoreLocatorContent({
                type: GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_GENERATION,
                payload: {
                    ...payload,
                    context: [{ [GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION]: galleryTitle }],
                },
            }),
        ]);

        return {
            pageUrl,
            title,
            metaDescription,
            twitterDescription,
            descriptions,
            ctaTitle,
            reviewsTitle,
            galleryTitle,
            gallerySubtitle,
            socialNetworksTitle,
            faqs,
        };
    }

    async generateSpecificPageContent<T extends GenerateStoreLocatorContentType>({
        type,
        storeLocatorRestaurantPage,
        storeLocatorOrganizationConfig,
        lang,
        params,
    }: {
        type: T;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
        lang: StoreLocatorLanguage;
        params?: GenerateStoreLocatorStorePageContentBodyDto['params'];
    }): Promise<AiStoreLocatorContentType<T>> {
        let payload = await this._getLambdaPayload({
            restaurantId: storeLocatorRestaurantPage.restaurantId.toString(),
            storeLocatorOrganizationConfig,
            lang,
        });

        payload = {
            ...payload,
            context: [...(payload.context ?? []), ...(params?.context ?? [])],
        };

        const generateFaqBlockPayload: GenerateStoreLocatorContentPayload['restaurantData'] = {
            ...payload,
            questionsTemplate: storeLocatorOrganizationConfig.blocksSettings.faq.questionsTemplate,
        };

        const headMetaDescriptionPayload = {
            ...payload,
            context: [
                ...(params?.context ?? []),
                { [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.information.title },
            ],
        };
        const galleryTitlePayload = {
            ...payload,
            context: [
                ...(params?.context ?? []),
                { [GenerateStoreLocatorContentType.H1_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.information.title },
            ],
        };
        const gallerySubtitlePayload = {
            ...payload,
            context: [
                ...(params?.context ?? []),
                {
                    [GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.gallery.title,
                },
            ],
        };
        const socialNetworksTitlePayload = {
            ...payload,
            context: [
                ...(params?.context ?? []),
                { [GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION]: storeLocatorRestaurantPage.blocks.reviews.title },
            ],
        };

        switch (type) {
            case GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_GENERATION:
                payload = headMetaDescriptionPayload;
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_GENERATION:
                payload = galleryTitlePayload;
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_GENERATION:
                payload = gallerySubtitlePayload;
                break;
            case GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_GENERATION:
                payload = socialNetworksTitlePayload;
                break;
            case GenerateStoreLocatorContentType.RESTAURANT_PAGE_URL_GENERATION:
            case GenerateStoreLocatorContentType.H1_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.HEAD_META_TWITTER_DESCRIPTION_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION:
            case GenerateStoreLocatorContentType.FAQ_BLOCK_GENERATION:
                payload = generateFaqBlockPayload;
                break;
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_GENERATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_GENERATION:
            case GenerateStoreLocatorContentType.MAP_BLOCK_TITLE_GENERATION:
            case GenerateStoreLocatorContentType.MAP_DESCRIPTION_GENERATION:
            case GenerateStoreLocatorContentType.MAP_TWITTER_DESCRIPTION_GENERATION:
            case GenerateStoreLocatorContentType.MAP_KEYWORDS_GENERATION:
                break;

            // Optimization
            case GenerateStoreLocatorContentType.HEAD_META_DESCRIPTION_OPTIMIZATION:
                payload = {
                    ...headMetaDescriptionPayload,
                    ...(params?.currentContent && { previousGeneration: params.currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_TITLE_OPTIMIZATION:
                payload = {
                    ...galleryTitlePayload,
                    ...(params?.currentContent && { previousGeneration: params.currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.GALLERY_BLOCK_SUBTITLE_OPTIMIZATION:
                payload = {
                    ...gallerySubtitlePayload,
                    ...(params?.currentContent && { previousGeneration: params.currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.SOCIAL_MEDIA_BLOCK_TITLE_OPTIMIZATION:
                payload = {
                    ...socialNetworksTitlePayload,
                    ...(params?.currentContent && { previousGeneration: params.currentContent }),
                };
                break;
            case GenerateStoreLocatorContentType.H1_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.CTA_BLOCK_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.REVIEWS_BLOCK_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_TITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION:
            case GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION:
            case GenerateStoreLocatorContentType.FAQ_BLOCK_QUESTION_OPTIMIZATION:
            case GenerateStoreLocatorContentType.FAQ_BLOCK_ANSWER_OPTIMIZATION:
                payload = {
                    ...payload,
                    ...(params?.currentContent && { previousGeneration: params.currentContent }),
                };
                break;
            default:
                break;
        }

        const response = await this.generateStoreLocatorContent({ type, payload });

        return response;
    }

    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>({
        type,
        payload,
    }: {
        type: T;
        payload: GenerateStoreLocatorContentPayload['restaurantData'];
    }): Promise<AiStoreLocatorContentType<T>> {
        const response = await this._aiStoreLocatorContentService.generateStoreLocatorContent(type, payload);

        return response.aiResponse;
    }

    private async _getLambdaPayload({
        restaurantId,
        storeLocatorOrganizationConfig,
        lang,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: StoreLocatorOrganizationConfiguration;
        lang: StoreLocatorLanguage;
    }): Promise<GenerateStoreLocatorContentPayload['restaurantData']> {
        const organizationKeywords = storeLocatorOrganizationConfig.aiSettings.keywords.map(({ text }) => text);
        const organizationBricks: string[] = storeLocatorOrganizationConfig.aiSettings.keywords.flatMap((keyword) =>
            keyword.bricks.map((brick) => brick[lang] || brick.text)
        );

        return await this.getLambdaPayloadFromOrganizationParams({
            restaurantId,
            organizationConfig: {
                organizationId: storeLocatorOrganizationConfig.organization.id,
                organizationName: storeLocatorOrganizationConfig.organization.name,
                keywords: organizationKeywords,
                tone: storeLocatorOrganizationConfig.aiSettings.tone,
                languageStyle: storeLocatorOrganizationConfig.aiSettings.languageStyle,
                specialAttributes: storeLocatorOrganizationConfig.aiSettings.specialAttributes,
                attributes: storeLocatorOrganizationConfig.aiSettings.attributes,
                bricks: organizationBricks,
            },
            lang,
        });
    }

    async getLambdaPayloadFromOrganizationParams({
        restaurantId,
        organizationConfig,
        lang,
    }: {
        restaurantId: string;
        organizationConfig: {
            organizationId: string;
            organizationName: string;
            keywords: string[];
            attributes: Pick<StoreLocatorOrganizationConfiguration['aiSettings']['attributes'][number], 'attributeId' | 'attributeName'>[];
            bricks: string[];
            tone: string[];
            languageStyle: string;
            specialAttributes: { restaurantId: string; text: string }[];
        };
        lang: StoreLocatorLanguage;
    }): Promise<GenerateStoreLocatorContentPayload['restaurantData']> {
        const {
            keywords: organizationKeywords,
            languageStyle,
            organizationName,
            specialAttributes,
            tone: organizationTone,
            attributes: organizationAttributes,
            bricks: organizationBricks,
        } = organizationConfig;

        const [restaurants, restaurantAttributes] = await Promise.all([
            this._restaurantsRepository.find({
                filter: { organizationId: toDbId(organizationConfig.organizationId), shouldNotHaveStoreLocatorPage: false },
                projection: { _id: 1, name: 1, address: 1 },
                options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY },
            }),
            this._restaurantAttributesRepository.find({
                filter: {
                    restaurantId: toDbId(restaurantId),
                    attributeValue: RestaurantAttributeValue.YES,
                },
                options: {
                    lean: true,
                    populate: [{ path: 'attribute' }],
                    readPreference: ReadPreferenceMode.SECONDARY,
                },
            }),
        ]);
        assert(restaurants.length > 0, 'No restaurant found for this organization');

        const restaurant = restaurants.find((r) => r._id.equals(toDbId(restaurantId)));

        if (!restaurant) {
            throw new MalouError(MalouErrorCode.RESTAURANT_NOT_FOUND, { message: 'No restaurant found for this restaurantId' });
        }

        const doesRestaurantShareLocationWithOthers = restaurants.some(
            (otherRestaurant) =>
                otherRestaurant._id.equals(toDbId(restaurantId)) &&
                otherRestaurant.address?.locality === restaurant.address?.locality &&
                otherRestaurant.address?.postalCode === restaurant.address?.postalCode
        );

        const keywords = uniq(organizationKeywords.map((keyword) => keyword.toLowerCase().trim()));
        const brandTone = uniq([...organizationTone, languageStyle].map((tone) => tone.toLowerCase().trim()));
        const restaurantOffers = this._getRestaurantOffersService
            .execute({
                organizationAttributes,
                restaurantAttributes,
                lang,
            })
            .map((text) => text.toLowerCase().trim());
        const specificsDirectives = specialAttributes
            .filter((specialAttribute) => specialAttribute.restaurantId.toString() === restaurantId)
            .map(({ text }) => text.toLowerCase().trim());
        const bricks = uniq(organizationBricks.map((brick) => brick.toLowerCase().trim()));

        return {
            restaurantName: restaurant.name,
            organizationName,
            address: {
                locality: restaurant.address?.locality,
                postalCode: restaurant.address?.postalCode,
                formattedAddress: restaurant.address?.formattedAddress,
                isNotUniqueAddress: doesRestaurantShareLocationWithOthers,
            },
            keywords,
            brandTone,
            bricks,
            restaurantOffers,
            specificsDirectives,
            language: lang,
        };
    }
}
