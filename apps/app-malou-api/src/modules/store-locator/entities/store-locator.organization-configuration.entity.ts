import { StoreLocatorOrganizationConfigurationResponseDto } from '@malou-io/package-dto';
import {
    EntityConstructor,
    isNotNil,
    PlatformKey,
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorCentralizationPageElementIds,
    StoreLocatorCommonElementIds,
    StoreLocatorFaqBlockQuestionType,
    StoreLocatorLanguage,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

export type StoreLocatorOrganizationConfigurationProps = Omit<
    EntityConstructor<StoreLocatorOrganizationConfiguration> & {
        id: string;
    },
    'desiredLanguages'
>;

interface StoreLocatorOrganizationConfigPagesStyles {
    store: Record<StoreLocatorRestaurantPageElementIds, string[]>;
    map: Record<StoreLocatorCentralizationPageElementIds, string[]>;
    storeDraft: Record<StoreLocatorRestaurantPageElementIds, string[]>;
    mapDraft: Record<StoreLocatorCentralizationPageElementIds, string[]>;
    common: Record<StoreLocatorCommonElementIds, string[]>;
}

export class StoreLocatorOrganizationConfiguration {
    id: string;
    organizationId: string;
    cloudfrontDistributionId: string;
    baseUrl: string;
    isLive: boolean;
    shouldDisplayWhiteMark: boolean;
    styles: {
        fonts: Array<{
            class: string;
            src: string;
            weight?: string;
            style?: string;
        }>;
        colors: Array<{
            class: string;
            value: string;
        }>;
        pages: StoreLocatorOrganizationConfigPagesStyles;
    };
    plugins?: {
        googleAnalytics?: {
            trackingId: string;
        };
    };
    organization: {
        id: string;
        name: string;
    };
    languages: {
        primary: StoreLocatorLanguage;
        secondary: StoreLocatorLanguage[];
    };
    desiredLanguages: StoreLocatorLanguage[];
    aiSettings: {
        tone: string[];
        languageStyle: StoreLocatorAiSettingsLanguageStyle;
        attributeIds: string[];
        restaurantKeywordIds: string[];
        specialAttributes: Array<{
            restaurantId: string;
            text: string;
        }>;
        attributes: Array<{
            id: string;
            attributeId: string;
            platformKey: PlatformKey;
            attributeName: {
                fr: string;
                en?: string;
                es?: string;
                it?: string;
            };
        }>;
        keywords: Array<{
            restaurantKeywordId: string;
            text: string;
            restaurantId: string;
            keywordId: string;
            bricks: {
                text: string;
                fr: string;
                en: string;
                it: string;
                es: string;
            }[];
        }>;
    };
    blocksSettings: {
        faq: {
            questionsTemplate: StoreLocatorFaqBlockQuestionType[];
        };
        reviews: {
            isOnlyFiveStarsReviews: boolean;
        };
    };

    constructor(props: StoreLocatorOrganizationConfigurationProps) {
        this.id = props.id;
        this.organizationId = props.organizationId;
        this.cloudfrontDistributionId = props.cloudfrontDistributionId;
        this.baseUrl = props.baseUrl;
        this.isLive = props.isLive;
        this.styles = {
            ...props.styles,
            pages: {
                ...props.styles.pages,
                store: props.styles.pages.store ?? {},
                map: props.styles.pages.map ?? {},
                storeDraft: props.styles.pages.storeDraft ?? {},
                mapDraft: props.styles.pages.mapDraft ?? {},
                common: props.styles.pages.common ?? {},
            },
        };
        this.plugins = props.plugins;
        this.languages = props.languages;
        this.desiredLanguages = [props.languages.primary, ...(props.languages.secondary ?? [])]
            .filter(isNotNil)
            .filter((lang) => lang !== StoreLocatorLanguage.UNDETERMINED);
        this.aiSettings = props.aiSettings;
        this.organization = props.organization;
        this.shouldDisplayWhiteMark = props.shouldDisplayWhiteMark ?? false;
        this.blocksSettings = props.blocksSettings;
    }

    toDto(): StoreLocatorOrganizationConfigurationResponseDto {
        return {
            id: this.id,
            organizationId: this.organizationId,
            organization: {
                id: this.organization.id,
                name: this.organization.name,
            },
            cloudfrontDistributionId: this.cloudfrontDistributionId,
            baseUrl: this.baseUrl,
            isLive: this.isLive,
            styles: this.styles,
            plugins: this.plugins,
            languages: this.languages,
            aiSettings: this.aiSettings,
            shouldDisplayWhiteMark: this.shouldDisplayWhiteMark,
        };
    }
}
