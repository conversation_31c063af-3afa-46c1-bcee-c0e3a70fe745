import { Builder } from 'builder-pattern';

import { IStoreLocatorOrganizationConfig, newDbId } from '@malou-io/package-models';
import {
    StoreLocatorAiSettingsLanguageStyle,
    StoreLocatorCentralizationPageElementIds,
    StoreLocatorCommonElementIds,
    StoreLocatorLanguage,
    StoreLocatorRestaurantPageElementIds,
} from '@malou-io/package-utils';

type StoreLocatorOrganizationConfigPayload = IStoreLocatorOrganizationConfig;

const _buildStoreLocatorOrganizationConfig = (storeLocatorOrganizationConfig: StoreLocatorOrganizationConfigPayload) =>
    Builder<StoreLocatorOrganizationConfigPayload>(storeLocatorOrganizationConfig);

export const getDefaultStoreLocatorOrganizationConfig = () =>
    _buildStoreLocatorOrganizationConfig({
        _id: newDbId(),
        organizationId: newDbId(),
        cloudfrontDistributionId: 'EXAMPLE_CLOUDFRONT_ID',
        baseUrl: 'https://example.com',
        isLive: false,
        styles: {
            fonts: [{ class: 'primary', src: 'https://example.io' }],
            colors: [{ class: 'primary', value: '#FFFFFF' }],
            pages: {
                store: {
                    [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-primary'],
                },
                map: {
                    [StoreLocatorCentralizationPageElementIds.MAP_AND_STORE_LIST_WRAPPER]: ['bg-secondary'],
                },
                common: {
                    [StoreLocatorCommonElementIds.WHITE_MARK_LOGO]: ['bg-white'],
                },
                storeDraft: {
                    [StoreLocatorRestaurantPageElementIds.INFORMATION_BANNER]: ['bg-secondary'],
                },
                mapDraft: {
                    [StoreLocatorCentralizationPageElementIds.MAP_AND_STORE_LIST_WRAPPER]: ['bg-secondary'],
                },
            },
        },
        languages: {
            primary: StoreLocatorLanguage.UNDETERMINED,
            secondary: [],
        },
        aiSettings: {
            tone: ['friendly'],
            languageStyle: StoreLocatorAiSettingsLanguageStyle.FORMAL,
            attributeIds: [],
            restaurantKeywordIds: [],
            specialAttributes: [],
        },
        blocksSettings: {
            faq: {
                questionsTemplate: [],
            },
            reviews: {
                isOnlyFiveStarsReviews: true,
            },
        },
        shouldDisplayWhiteMark: false,
        createdAt: new Date(),
        updatedAt: new Date(),
    });
