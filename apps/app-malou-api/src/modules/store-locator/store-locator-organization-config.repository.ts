import { singleton } from 'tsyringe';

import {
    EntityRepository,
    IStoreLocatorOrganizationConfig,
    IStoreLocatorOrganizationConfigPopulated,
    StoreLocatorOrganizationConfigModel,
    toDbId,
} from '@malou-io/package-models';
import { MalouErrorCode, StoreLocatorAiSettingsLanguageStyle } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { OrganizationConfigurationAiSettingsUpdate } from ':modules/store-locator/use-cases/update-organization-config-ai-settings/update-organization-config-ai-settings.use-case';

import { StoreLocatorOrganizationConfiguration } from './entities/store-locator.organization-configuration.entity';

const DEFAULT_POPULATE_OPTIONS = [
    {
        path: 'aiSettings' as const,
        populate: [
            {
                path: 'attributes' as const,
                options: { projection: { _id: 1, attributeId: 1, attributeName: 1, platformKey: 1 }, lean: true },
            },
            {
                path: 'restaurantKeywords' as const,
                populate: [
                    {
                        path: 'keyword' as const,
                        populate: [
                            {
                                path: 'bricks' as const,
                                populate: [
                                    { path: 'translations' as const, options: { lean: true, projection: { fr: 1, en: 1, es: 1, it: 1 } } },
                                ],
                                options: {
                                    projection: { text: 1, translationsId: 1 },
                                    lean: true,
                                },
                            },
                        ],
                        options: { projection: { _id: 1, keywordId: 1, text: 1, bricks: 1 }, lean: true },
                    },
                ],
                options: { projection: { _id: 1, keywordId: 1, keyword: 1, restaurantId: 1 }, lean: true },
            },
        ] as any,
    },
    {
        path: 'organization' as const,
        options: { lean: true },
    },
];

@singleton()
export class StoreLocatorOrganizationConfigRepository extends EntityRepository<IStoreLocatorOrganizationConfig> {
    constructor() {
        super(StoreLocatorOrganizationConfigModel);
    }

    async getOrganizationConfiguration(organizationId: string): Promise<StoreLocatorOrganizationConfiguration> {
        const document = await this.findOne({
            filter: { organizationId: toDbId(organizationId) },
            options: { populate: DEFAULT_POPULATE_OPTIONS, lean: true },
        });

        if (!document) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: `Store locator organization configuration not found for organization ID: ${organizationId}`,
                metadata: { organizationId },
            });
        }

        return this.toEntity(document as IStoreLocatorOrganizationConfigPopulated);
    }

    async getAllOrganizationConfigurations(): Promise<StoreLocatorOrganizationConfiguration[]> {
        const documents = await this.find({
            filter: {},
            options: { populate: DEFAULT_POPULATE_OPTIONS, lean: true },
        });

        return documents.map((doc) => this.toEntity(doc as IStoreLocatorOrganizationConfigPopulated));
    }

    async updateAiSettings(
        organizationId: string,
        aiSettings: OrganizationConfigurationAiSettingsUpdate
    ): Promise<StoreLocatorOrganizationConfiguration> {
        const aiSettingsForDb = this._toAiSettingsDocument(aiSettings);

        const document = await this.findOneAndUpdate({
            filter: { organizationId: toDbId(organizationId) },
            update: { aiSettings: aiSettingsForDb },
            options: { populate: DEFAULT_POPULATE_OPTIONS, lean: true },
        });

        if (!document) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: `Store locator organization configuration not found for organization ID: ${organizationId}`,
                metadata: { organizationId },
            });
        }

        return this.toEntity(document as IStoreLocatorOrganizationConfigPopulated);
    }

    async updateLanguages(
        organizationId: string,
        languages: { primary: string; secondary: string[] }
    ): Promise<StoreLocatorOrganizationConfiguration> {
        const document = await this.findOneAndUpdate({
            filter: { organizationId: toDbId(organizationId) },
            update: { languages },
            options: { populate: DEFAULT_POPULATE_OPTIONS, lean: true },
        });

        if (!document) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, {
                message: `Store locator organization configuration not found for organization ID: ${organizationId}`,
                metadata: { organizationId },
            });
        }

        return this.toEntity(document as IStoreLocatorOrganizationConfigPopulated);
    }

    toEntity(document: IStoreLocatorOrganizationConfigPopulated): StoreLocatorOrganizationConfiguration {
        return new StoreLocatorOrganizationConfiguration({
            id: document._id.toString(),
            organizationId: document.organizationId.toString(),
            cloudfrontDistributionId: document.cloudfrontDistributionId,
            baseUrl: document.baseUrl,
            isLive: document.isLive,
            shouldDisplayWhiteMark: document.shouldDisplayWhiteMark,
            styles: document.styles as StoreLocatorOrganizationConfiguration['styles'],
            plugins: document.plugins,
            organization: {
                id: document.organization._id.toString(),
                name: document.organization.name,
            },
            languages: {
                primary: document.languages.primary,
                secondary: document.languages.secondary ?? [],
            },
            aiSettings: {
                tone: document.aiSettings.tone,
                languageStyle: document.aiSettings.languageStyle || StoreLocatorAiSettingsLanguageStyle.FORMAL,
                attributeIds: document.aiSettings.attributeIds ?? [],
                restaurantKeywordIds: document.aiSettings.restaurantKeywordIds?.map((id) => id.toString()),
                specialAttributes:
                    document.aiSettings.specialAttributes?.map((attr) => ({
                        restaurantId: attr.restaurantId.toString(),
                        text: attr.text,
                    })) || [],
                keywords: document.aiSettings.restaurantKeywords.map((restaurantKeyword) => ({
                    restaurantKeywordId: restaurantKeyword._id.toString(),
                    keywordId: restaurantKeyword.keywordId.toString(),
                    text: restaurantKeyword.keyword?.text,
                    restaurantId: restaurantKeyword.restaurantId.toString(),
                    bricks: restaurantKeyword.keyword.bricks.map((brick) => ({
                        text: brick.text,
                        fr: brick.translations?.fr || brick.text,
                        en: brick.translations?.en || brick.text,
                        it: brick.translations?.it || brick.text,
                        es: brick.translations?.es || brick.text,
                    })),
                })),
                attributes: document.aiSettings.attributes.map((attribute) => ({
                    id: attribute._id.toString(),
                    attributeId: attribute?.attributeId,
                    platformKey: attribute?.platformKey,
                    attributeName: {
                        fr: attribute?.attributeName.fr ?? '',
                        en: attribute?.attributeName.en ?? undefined,
                        es: attribute?.attributeName.es ?? undefined,
                        it: attribute?.attributeName.it ?? undefined,
                    },
                })),
            },
            blocksSettings: {
                faq: {
                    questionsTemplate: document.blocksSettings.faq.questionsTemplate,
                },
                reviews: {
                    isOnlyFiveStarsReviews: document.blocksSettings.reviews.isOnlyFiveStarsReviews,
                },
            },
        });
    }

    private _toAiSettingsDocument(aiSettings: OrganizationConfigurationAiSettingsUpdate): IStoreLocatorOrganizationConfig['aiSettings'] {
        return {
            tone: aiSettings.tone ?? [],
            languageStyle: aiSettings.languageStyle || StoreLocatorAiSettingsLanguageStyle.FORMAL,
            attributeIds: aiSettings.attributeIds ?? [],
            restaurantKeywordIds: aiSettings.restaurantKeywordIds?.map(toDbId) ?? [],
            specialAttributes:
                aiSettings.specialAttributes?.map((attr) => ({
                    restaurantId: toDbId(attr.restaurantId),
                    text: attr.text,
                })) ?? [],
        };
    }
}
