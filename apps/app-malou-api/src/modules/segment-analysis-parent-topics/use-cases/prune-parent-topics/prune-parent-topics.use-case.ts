import { singleton } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { AiInteractionRelatedEntityCollection, AiInteractionType } from '@malou-io/package-utils';

import { GenericAiServiceCompletionResponse } from ':microservices/ai-lambda-template/generic-ai-service';
import { AiSemanticAnalysisTopicPruningService } from ':microservices/ai-semantic-analysis-topic-pruning.service';
import { AiReviewAnalysisCategory } from ':microservices/ai-semantic-analysis.service';
import { AiInteractionsRepository } from ':modules/ai-interactions/ai-interactions.repository';
import { SemanticAnalysisTopicPruningPayload, SemanticAnalysisTopicPruningPayloadTopic } from ':modules/ai/interfaces/ai.interfaces';
import { AiCompletionMapper } from ':modules/ai/mappers/ai-completion.mapper';
import { ReviewSemanticAnalysisMapper } from ':modules/review-analyses/platforms/openai/mapper';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';

const DEFAULT_PRUNING_MONTH_AGO_THRESHOLD = 6;

@singleton()
export class PruneParentTopicsUseCase {
    constructor(
        private readonly _aiSemanticAnalysisTopicPruningService: AiSemanticAnalysisTopicPruningService,
        private readonly _segmentAnalysisParentTopicsRepository: SegmentAnalysisParentTopicsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _aiInteractionsRepository: AiInteractionsRepository,
        private readonly _aiCompletionMapper: AiCompletionMapper
    ) {}

    async execute(restaurantId: string, pruningDate: Date): Promise<void> {
        try {
            const payload = await this._computePayload(restaurantId, pruningDate);
            const { aiResponse, aiInteractionDetails } = await this._aiSemanticAnalysisTopicPruningService.generatePruning(payload);
            await this._segmentAnalysisParentTopicsRepository.updateManyAiVisible(aiResponse.topics);
            if (aiInteractionDetails.length) {
                await this._saveInteractions({
                    restaurantId,
                    aiInteractions: aiInteractionDetails,
                });
            }
        } catch (error) {
            console.error(`Error while pruning topics for restaurant ${restaurantId}:`, error);
            throw error;
        }
    }

    private async _computePayload(restaurantId: string, pruningDate: Date): Promise<SemanticAnalysisTopicPruningPayload> {
        const reviewsWithTextCount = await this._reviewsRepository.getReviewsWithTextCountByRestaurantId(
            restaurantId,
            DEFAULT_PRUNING_MONTH_AGO_THRESHOLD
        );
        const reviewsWithCommentsCount = reviewsWithTextCount
            .sort((a, b) => {
                if (a.year === b.year) {
                    return b.month - a.month;
                }
                return b.year - a.year;
            })
            .map((item) => item.count);
        const parentTopics = await this._segmentAnalysisParentTopicsRepository.getSegmentAnalysesParentTopicWithReviewCount(restaurantId);
        const topics = parentTopics.reduce(
            (acc, topic) => {
                const lambdaCategory = ReviewSemanticAnalysisMapper.mapToLambdaSegmentCategory(topic.category);
                if (!acc[lambdaCategory]) {
                    acc[lambdaCategory] = [];
                }
                acc[lambdaCategory].push({
                    topicId: topic.topicId.toString(),
                    title: topic.title,
                    createdAt: topic.createdAt,
                    lastLinkedAt: topic.lastLinkedAt ?? new Date(),
                    numberOfLinkedReviews: topic.numberOfLinkedReviews,
                    isAiVisible: topic.isAiVisible ?? true,
                    isUserInput: topic.isUserInput,
                    isFavorite: topic.isFavorite,
                    subCategory: topic.subCategory,
                } as SemanticAnalysisTopicPruningPayloadTopic);
                return acc;
            },
            {} as Record<AiReviewAnalysisCategory, SemanticAnalysisTopicPruningPayloadTopic[]>
        );
        return {
            relatedEntityCollection: AiInteractionRelatedEntityCollection.SEGMENT_ANALYSES,
            type: AiInteractionType.TOPIC_PRUNING,
            restaurantData: {
                reviewsWithCommentsCount,
                topics,
                currentDate: pruningDate,
            },
        };
    }

    private async _saveInteractions({
        restaurantId,
        aiInteractions,
    }: {
        restaurantId: string;
        aiInteractions: GenericAiServiceCompletionResponse[];
    }): Promise<void> {
        if (!aiInteractions || !aiInteractions.length) {
            return;
        }
        const aiInteractionsToSave = aiInteractions.map((aiInteraction) => ({
            ...this._aiCompletionMapper.mapGeneratorCompletionToAiInteraction(aiInteraction, toDbId(restaurantId)),
            restaurantId,
            userId: undefined,
            relatedEntityId: aiInteraction.relatedEntityId,
            relatedEntityCollection:
                aiInteraction.relatedEntityCollection || AiInteractionRelatedEntityCollection.SEGMENT_ANALYSIS_PARENT_TOPICS,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
        await this._aiInteractionsRepository.createAiInteractions(aiInteractionsToSave);
    }
}
