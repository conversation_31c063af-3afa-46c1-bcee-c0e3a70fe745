import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import {
    AiInteractionRelatedEntityCollection,
    AiInteractionType,
    PlatformKey,
    ReviewAnalysisSentiment,
    ReviewAnalysisSubCategory,
    ReviewAnalysisTag,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { AiSemanticAnalysisTopicPruningService } from ':microservices/ai-semantic-analysis-topic-pruning.service';
import { AiReviewAnalysisCategory } from ':microservices/ai-semantic-analysis.service';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { getDefaultSegmentAnalysis } from ':modules/segment-analyses/tests/segment-analysis.builder';
import { SegmentAnalysisParentTopicsRepository } from ':modules/segment-analysis-parent-topics/segment-analysis-parent-topics.repository';
import { getDefaultSegmentAnalysisParentTopics } from ':modules/segment-analysis-parent-topics/tests/segment-analysis-parent-topics.builder';
import { PruneParentTopicsUseCase } from ':modules/segment-analysis-parent-topics/use-cases/prune-parent-topics/prune-parent-topics.use-case';

describe('PruneParentTopicsUseCase', () => {
    let pruneParentTopicsUseCase: PruneParentTopicsUseCase;
    let mockAiService: jest.Mocked<AiSemanticAnalysisTopicPruningService>;

    beforeAll(() => {
        init({
            aiServiceMock: {
                generatePruning: jest.fn().mockResolvedValue({ aiResponse: { topics: [] }, aiInteractionDetails: [] }),
            },
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    describe('execute', () => {
        it('should call AI service with correct payload containing restaurant reviews and parent topics data', async () => {
            const restaurantId = newDbId();
            const topicId1 = newDbId();
            const topicId2 = newDbId();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).name('Test Restaurant').build()];
                        },
                    },
                    reviews: {
                        data() {
                            const threeMonthsAgo = DateTime.now().minus({ months: 3 }).toJSDate();

                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.GMB)
                                    .text('Great food and service')
                                    .socialCreatedAt(threeMonthsAgo)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Amazing atmosphere')
                                    .socialCreatedAt(threeMonthsAgo)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    ._id(topicId1)
                                    .restaurantId(restaurantId)
                                    .name('Service Quality')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .isAiVisible(true)
                                    .isUserInput(false)
                                    .isFavorite(false)
                                    .build(),
                                getDefaultSegmentAnalysisParentTopics()
                                    ._id(topicId2)
                                    .restaurantId(restaurantId)
                                    .name('Food Quality')
                                    .category(ReviewAnalysisTag.FOOD)
                                    .subcategory(ReviewAnalysisSubCategory.MENU_ITEMS)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .isAiVisible(true)
                                    .isUserInput(true)
                                    .isFavorite(true)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .reviewSocialId('review1')
                                    .build(),
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[1]._id])
                                    .reviewSocialId('review2')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            await pruneParentTopicsUseCase.execute(restaurantId.toString(), new Date());

            expect(mockAiService.generatePruning).toHaveBeenCalledTimes(1);

            const calledPayload = mockAiService.generatePruning.mock.calls[0][0];

            expect(calledPayload).toMatchObject({
                relatedEntityCollection: AiInteractionRelatedEntityCollection.SEGMENT_ANALYSES,
                type: AiInteractionType.TOPIC_PRUNING,
                restaurantData: {
                    reviewsWithCommentsCount: [0, 0, 2, 0, 0, 0],
                    topics: {
                        [AiReviewAnalysisCategory.CUISINE]: [
                            {
                                topicId: topicId2.toString(),
                                title: 'Food Quality',
                                isAiVisible: true,
                                isUserInput: true,
                                isFavorite: true,
                                subCategory: ReviewAnalysisSubCategory.MENU_ITEMS,
                            },
                        ],
                        [AiReviewAnalysisCategory.SERVICE]: [
                            {
                                topicId: topicId1.toString(),
                                title: 'Service Quality',
                                isAiVisible: true,
                                isUserInput: false,
                                isFavorite: false,
                                subCategory: undefined,
                            },
                        ],
                    },
                },
            });
        });

        it('should handle restaurant with no parent topics', async () => {
            const restaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).name('Empty Restaurant').build()];
                        },
                    },
                    reviews: {
                        data() {
                            return [];
                        },
                    },
                },
            });

            await testCase.build();

            await pruneParentTopicsUseCase.execute(restaurantId.toString(), new Date());

            expect(mockAiService.generatePruning).toHaveBeenCalledTimes(1);

            const calledPayload = mockAiService.generatePruning.mock.calls[0][0];

            expect(calledPayload.restaurantData.topics).toEqual({});
            expect(calledPayload.restaurantData.reviewsWithCommentsCount).toEqual(expect.any(Array));
        });

        it('should update isAiVisible for topics', async () => {
            const restaurantId = newDbId();
            const topicId = newDbId();

            init({
                aiServiceMock: {
                    generatePruning: jest.fn().mockResolvedValue({
                        aiResponse: {
                            topics: [
                                {
                                    topicId: topicId.toString(),
                                    isAiVisible: false,
                                },
                            ],
                        },
                        aiInteractionDetails: [],
                    }),
                },
            });

            const testCase = new TestCaseBuilderV2<'restaurants' | 'reviews' | 'segmentAnalysisParentTopics' | 'segmentAnalyses'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant()._id(restaurantId).name('Test Restaurant').build()];
                        },
                    },
                    reviews: {
                        data() {
                            const threeMonthsAgo = DateTime.now().minus({ months: 3 }).toJSDate();

                            return [
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.GMB)
                                    .text('Great food and service')
                                    .socialCreatedAt(threeMonthsAgo)
                                    .build(),
                                getDefaultReview()
                                    .restaurantId(restaurantId)
                                    .key(PlatformKey.FACEBOOK)
                                    .text('Amazing atmosphere')
                                    .socialCreatedAt(threeMonthsAgo)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalysisParentTopics: {
                        data() {
                            return [
                                getDefaultSegmentAnalysisParentTopics()
                                    ._id(topicId)
                                    .restaurantId(restaurantId)
                                    .name('Service Quality')
                                    .category(ReviewAnalysisTag.SERVICE)
                                    .sentiment(ReviewAnalysisSentiment.POSITIVE)
                                    .isAiVisible(true)
                                    .isUserInput(false)
                                    .isFavorite(false)
                                    .build(),
                            ];
                        },
                    },
                    segmentAnalyses: {
                        data(dependencies) {
                            return [
                                getDefaultSegmentAnalysis()
                                    .segmentAnalysisParentTopicIds([dependencies.segmentAnalysisParentTopics()[0]._id])
                                    .reviewSocialId('review1')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();

            await pruneParentTopicsUseCase.execute(restaurantId.toString(), new Date());

            const segmentAnalysisParentTopicsRepository = container.resolve(SegmentAnalysisParentTopicsRepository);
            const parentTopic = await segmentAnalysisParentTopicsRepository.find({
                filter: { _id: topicId },
                options: { lean: true },
            });
            expect(parentTopic[0].isAiVisible).toBe(false);
        });
    });

    const init = ({ aiServiceMock }: { aiServiceMock: jest.Mocked<AiSemanticAnalysisTopicPruningService> }) => {
        container.clearInstances();

        registerRepositories([
            'RestaurantsRepository',
            'ReviewsRepository',
            'SegmentAnalysisParentTopicsRepository',
            'SegmentAnalysesRepository',
        ]);

        mockAiService = aiServiceMock;

        container.registerInstance(AiSemanticAnalysisTopicPruningService, mockAiService);

        pruneParentTopicsUseCase = container.resolve(PruneParentTopicsUseCase);
    };
});
