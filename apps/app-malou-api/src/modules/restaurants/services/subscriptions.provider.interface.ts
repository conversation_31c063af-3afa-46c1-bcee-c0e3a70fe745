import {
    GetLocationsByOrganizationRequest,
    GetLocationsByOrganizationResponse,
    UpdateSubscriptionsProviderLocationRequest,
} from ':modules/restaurants/services/subscriptions.provider.interfaces';
import { HyperlineApiCustomer } from ':providers/hyperline/hyperline.interfaces';

export interface SubscriptionsProvider {
    /**
     * Get all locations for an organization by its subscriptionsProviderId
     */
    getLocationsByOrganization(request: GetLocationsByOrganizationRequest): Promise<GetLocationsByOrganizationResponse>;

    /**
     * Update a location's malouRestaurantId field
     */
    updateSubscriptionsProviderLocation(request: UpdateSubscriptionsProviderLocationRequest): Promise<void>;

    getCustomers(skip: number): Promise<HyperlineApiCustomer[]>;
}
