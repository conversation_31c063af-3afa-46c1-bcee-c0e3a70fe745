import { DateTime } from 'luxon';
import { PipelineStage } from 'mongoose';

import { FeedbackMessageType } from '@malou-io/package-utils';

export const feedbackMessageCountProjectionStages = [
    {
        $lookup: {
            from: 'feedbacks',
            localField: 'feedbackId',
            foreignField: '_id',
            as: 'feedbacks',
        },
    },
    {
        $unwind: {
            path: '$feedbacks',
            preserveNullAndEmptyArrays: true,
        },
    },
    {
        $addFields: {
            // feedbackMessageCount is the count of opened feedback messages
            feedbackMessageCount: {
                $cond: {
                    if: { $eq: ['$feedbacks.isOpen', true] },
                    then: {
                        $size: {
                            // Only count feedback messages of type TEXT
                            $filter: {
                                input: { $ifNull: ['$feedbacks.feedbackMessages', []] },
                                as: 'feedbackMessage',
                                cond: {
                                    $eq: ['$$feedbackMessage.type', FeedbackMessageType.TEXT],
                                },
                            },
                        },
                    },
                    else: 0,
                },
            },
        },
    },
] as const;

export const sortStage = { $sort: { sortDate: -1 } } as const;

export const limitStage = (limit: number) => ({ $limit: limit }) as const;

export const platformsKeysProjectionStage = {
    $addFields: {
        platformKeys: { $cond: ['$key', ['$key'], '$keys'] },
    },
} as const;

export const firstAttachmentPopulationStages = [
    {
        $addFields: {
            firstAttachmentId: {
                $first: '$attachments',
            },
        },
    },
    {
        $lookup: {
            from: 'media',
            localField: 'firstAttachmentId',
            foreignField: '_id',
            as: 'firstAttachment',
        },
    },
    {
        $unwind: {
            path: '$firstAttachment',
            preserveNullAndEmptyArrays: true,
        },
    },
] as const;

// get the most recent (last positioned in the array) code that is not null
export const errorStage = {
    $addFields: {
        mostRecentPublicationErrorCode: {
            $getField: {
                field: 'code',
                input: {
                    $arrayElemAt: [
                        {
                            $filter: {
                                input: '$publicationErrors',
                                as: 'item',
                                // Use $ifNull to normalize missing fields to null.
                                // Without this, {$ne: ["$$item.code", null]} would still include documents
                                // where "code" is missing, because "missing" != null in MongoDB.
                                // Example: {code: null} → filtered out, but { } (no code field) → would slip through.
                                cond: { $ne: [{ $ifNull: ['$$item.code', null] }, null] },
                            },
                        },
                        -1,
                    ],
                },
            },
        },
    },
} as const;

export const projectSocialPostItemStage = {
    $project: {
        _id: 1,
        title: 1,
        text: 1,
        platformKeys: 1,
        published: 1,
        isPublishing: 1,
        postType: 1,
        feedbackMessageCount: 1,
        plannedPublicationDate: 1,
        firstAttachment: 1,
        socialAttachments: 1,
        hashtags: 1,
        error: 1,
        socialLink: 1,
        socialCreatedAt: 1,
        sortDate: 1,
        author: 1,
        mostRecentPublicationErrorCode: 1,
        bindingId: 1,
        reelThumbnailFromMedia: 1,
        reelThumbnailFromFrame: 1,
    },
} as const;

export const projectFeedItemStage = {
    $project: {
        _id: 1,
        firstAttachment: 1,
        socialAttachments: 1,
        published: 1,
        plannedPublicationDate: 1,
        postType: 1,
        socialCreatedAt: 1,
        updatedAt: 1,
        sortDate: 1,
        reelThumbnailFromMedia: 1,
        reelThumbnailFromFrame: 1,
    },
} as const;

export const countSocialPostsStages = () => [
    {
        $facet: {
            total: [
                {
                    $count: 'total',
                },
            ],
            error: [
                {
                    $match: {
                        published: 'error',
                        createdAt: { $gt: DateTime.now().minus({ months: 6 }).toJSDate() },
                    },
                },
                { $count: 'error' },
            ],
            draft: [
                {
                    $match: {
                        published: 'draft',
                    },
                },
                {
                    $count: 'draft',
                },
            ],
            feedbacks: [
                {
                    $match: {
                        feedbackMessageCount: {
                            $gt: 0,
                        },
                    },
                },
                {
                    $count: 'feedbacks',
                },
            ],
        },
    },
    {
        $project: {
            total: {
                $arrayElemAt: ['$total.total', 0],
            },
            error: {
                $arrayElemAt: ['$error.error', 0],
            },
            draft: {
                $arrayElemAt: ['$draft.draft', 0],
            },
            feedbacks: {
                $arrayElemAt: ['$feedbacks.feedbacks', 0],
            },
        },
    },
];

export const reelThumbnailPopulationStages: PipelineStage[] = [
    {
        $lookup: {
            from: 'media',
            localField: 'thumbnail',
            foreignField: '_id',
            as: 'thumbnail',
        },
    },
    {
        $unwind: {
            path: '$thumbnail',
            preserveNullAndEmptyArrays: true,
        },
    },
    {
        $lookup: {
            from: 'media',
            localField: 'reelThumbnailFromMedia',
            foreignField: '_id',
            as: 'reelThumbnailFromMedia',
        },
    },
    {
        $unwind: {
            path: '$reelThumbnailFromMedia',
            preserveNullAndEmptyArrays: true,
        },
    },
    {
        $lookup: {
            from: 'media',
            localField: 'reelThumbnailFromFrame.media',
            foreignField: '_id',
            as: 'reelThumbnailFromFrame',
        },
    },
    {
        $unwind: {
            path: '$reelThumbnailFromFrame',
            preserveNullAndEmptyArrays: true,
        },
    },
];
