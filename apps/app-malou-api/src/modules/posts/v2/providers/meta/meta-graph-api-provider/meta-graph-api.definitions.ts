import { z } from 'zod';

/**
 * Some information about VIDEO_* errors:
 * https://developers.facebook.com/docs/video-api/guides/reels-publishing/#error-codes
 */
export const MetaGraphApiError = {
    CANNOT_VALIDATE_ERROR_RESPONSE: 'CANNOT_VALIDATE_ERROR_RESPONSE',
    CANNOT_VALIDATE_RESPONSE: 'CANNOT_VALIDATE_RESPONSE',
    INVALID_TOKEN: 'INVALID_TOKEN',
    MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION: 'MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION',
    UNKNOWN_ERROR: 'UNKNOWN_ERROR',
    USER_MISSING_APPROPRIATE_ROLE: 'USER_MISSING_APPROPRIATE_ROLE',
    USER_NEEDS_TO_LOG_IN: 'USER_NEEDS_TO_LOG_IN',
    USER_NEEDS_TO_LOG_IN_TO_IG_APP: 'USER_NEEDS_TO_LOG_IN_TO_IG_APP',
    MEDIA_CREATION_FAILED: 'MEDIA_CREATION_FAILED',
    INVALID_LOCATION: 'INVALID_LOCATION',
    USER_NOT_VISIBLE: 'USER_NOT_VISIBLE', // might be collaboration problem with alcohol related content
} as const;

export type IMetaGraphApiError = (typeof MetaGraphApiError)[keyof typeof MetaGraphApiError];

export interface MetaGraphApiErrorObject {
    code: IMetaGraphApiError;
    stringifiedRawError?: string;
}

export interface MetaGraphApiRequestOptions {
    method: 'GET' | 'POST';

    /** The path of the HTTP request (without the domain name) */
    endpoint: string;

    queryParams: Record<string, string>;

    /** Defaults to 'graph.facebook.com' but can be overriden here (to upload reels for instance). */
    hostname?: string;

    headers?: Record<string, string>;
}

// Non exhaustive error codes
// https://developers.facebook.com/docs/graph-api/guides/error-handling/
export enum MetaApiErrorCode {
    SESSION = 102,
    UNKNOWN = 1,
    SERVICE = 2,
    METHOD = 3,
    TOO_MANY_CALLS = 4,
    USER_TOO_MANY_CALLS = 17,
    PERMISSION_DENIED = 10,
    TOKEN_EXPIRED = 190,
    PERMISSION = 200, // can effectively be 200 to 299
    APP_LIMIT_REACHED = 341,
    POLICIES_VIOLATION = 368,
    DUPLICATE_POST = 506,
    ERROR_POSTING_LINK = 1609005,
}

// Non exhaustive error sub-codes
// https://developers.facebook.com/docs/graph-api/guides/error-handling/
export enum MetaApiErrorSubcode {
    APP_NOT_INSTALLED = 458,
    USER_CHECKPOINT = 459,
    PASSWORD_CHANGED = 460,
    EXPIRED = 463,
    UNCONFIRMED_USER = 464,
    INVALID_ACCESS_TOKEN = 467,
    INVALID_SESSION = 492,
    MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION = 2108006,
    INVALID_LOCATION = 2207019,
}
// Non exhaustive error couples (code and subcode)
// https://developers.facebook.com/docs/instagram-platform/instagram-graph-api/reference/error-codes/
export const metaIgErrorMapToMetaGraphApiError: Partial<Record<IMetaGraphApiError, { code: number; subcode: number }>> = {
    [MetaGraphApiError.USER_NEEDS_TO_LOG_IN_TO_IG_APP]: { code: 25, subcode: 2207050 },
    [MetaGraphApiError.MEDIA_CREATION_FAILED]: { code: -1, subcode: 2207032 }, // "Échec de la création du média, veuillez essayer de le recréer"
    [MetaGraphApiError.USER_NOT_VISIBLE]: { code: 210, subcode: 2207066 },
};

export const tokenRelatedErrorCodes = [MetaApiErrorCode.TOKEN_EXPIRED];

export const tokenRelatedErrorSubCodes = [
    MetaApiErrorSubcode.APP_NOT_INSTALLED,
    MetaApiErrorSubcode.USER_CHECKPOINT,
    MetaApiErrorSubcode.PASSWORD_CHANGED,
    MetaApiErrorSubcode.EXPIRED,
    MetaApiErrorSubcode.UNCONFIRMED_USER,
    MetaApiErrorSubcode.INVALID_ACCESS_TOKEN,
    MetaApiErrorSubcode.INVALID_SESSION,
];

export const metaGraphApiErrorResponseValidator = z.object({
    error: z.object({
        message: z.string().optional(),
        type: z.string().optional(),
        code: z.number().optional(),
        error_subcode: z.number().optional(),
        error_user_title: z.string().optional(),
        error_user_msg: z.string().optional(),
        fbtrace_id: z.string().optional(),
    }),
});
