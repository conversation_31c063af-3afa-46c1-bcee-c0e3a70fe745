import { intersection } from 'lodash';
import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPopulatedPost } from '@malou-io/package-models';
import { errorReplacer, removeAndAddHashtagsToText, RetryError, retryResult } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiCredentialsHandlerErrorCodes } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    MetaGraphApiHelperEndpoint,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

// TODO posts-v2 write a test for this
@singleton()
export class PublishCarouselOnInstagramService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        post: IPopulatedPost,
        platform: Platform,
        credentialId: string,
        medias: { url: string; type: 'photo' | 'video' }[]
    ): Promise<Result<{ mediaId: string }, MetaGraphApiHelperErrorObject>> {
        const igUserId = platform.socialId;
        assert(igUserId, 'Missing socialId on platform');

        const results: Result<{ containerId: string }, MetaGraphApiHelperErrorObject>[] = [];
        for (const [index, media] of medias.entries()) {
            if (media.type === 'photo') {
                const res = await this._metaGraphApiHelper.createIgPhotoContainerForCarousel(
                    credentialId,
                    igUserId,
                    media.url,
                    undefined,
                    undefined,
                    post.userTagsList?.[index] ?? undefined
                );
                results.push(res);
            } else if (media.type === 'video') {
                const userTags = undefined; // Current bug in Meta Graph API https://developers.facebook.com/support/bugs/1187206212645170/
                // The user tags are not supported for video containers in carousel but it should be. In the Instagram UI it is supported.
                // So we need to pass undefined here to avoid the error, but when the bug is fixed, replace with:
                // [post.userTagsList?.[index]?.[0]]
                const res = await this._metaGraphApiHelper.createIgVideoContainerForCarousel(
                    credentialId,
                    igUserId,
                    media.url,
                    undefined,
                    undefined,
                    userTags
                );
                results.push(res);
            }
        }
        const resultsCombined = Result.combine(results);
        if (resultsCombined.isErr()) {
            return err(resultsCombined.error);
        }

        const containerStatuesRes = await retryResult(
            async () => {
                // todo posts-v2 remove containers that are finished from the calls
                const promises = resultsCombined.value.map((r) =>
                    this._metaGraphApiHelper.getIgContainer(credentialId, igUserId, r.containerId)
                );
                const containerStatues = await Promise.all(promises);
                return Result.combine(containerStatues);
            },
            {
                attempts: 15,
                backoffStrategy: 'exponential',
                isSuccess: (res) => res.every((r) => r.status_code === 'FINISHED'),
                shouldRetrySuccess: (res) =>
                    intersection(
                        res.map((r) => r.status_code),
                        ['ERROR', 'EXPIRED']
                    ).length === 0,
            }
        );
        if (containerStatuesRes.isErr()) {
            if (containerStatuesRes.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS) {
                return err({
                    endpoint: MetaGraphApiHelperEndpoint.GET_IG_CONTAINER,
                    code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                    stringifiedRawError: JSON.stringify(containerStatuesRes.error, errorReplacer),
                });
            }
            assert(containerStatuesRes.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            assert(containerStatuesRes.error.lastResult.isErr());
            return err(containerStatuesRes.error.lastResult.error);
        }

        const containerIds = resultsCombined.value.map((e) => e.containerId);

        const selectedHashtagTexts = post.hashtags?.selected?.map((hashtag) => hashtag.text) ?? [];
        const textWithHashtags = removeAndAddHashtagsToText(post.text ?? '', selectedHashtagTexts);

        const carouselContainerIdRes = await this._metaGraphApiHelper.createIgCarouselContainer(
            credentialId,
            igUserId,
            containerIds,
            textWithHashtags,
            post.location?.id,
            post.instagramCollaboratorsUsernames
        );
        if (carouselContainerIdRes.isErr()) {
            return err(carouselContainerIdRes.error);
        }

        const carouselContainerStatusRes = await retryResult(
            () => this._metaGraphApiHelper.getIgContainer(credentialId, igUserId, carouselContainerIdRes.value.containerId),
            {
                attempts: 15,
                backoffStrategy: 'exponential',
                isSuccess: (res) => res.status_code === 'FINISHED',
                shouldRetrySuccess: (res) => !['ERROR', 'EXPIRED'].includes(res.status_code),
            }
        );
        if (carouselContainerStatusRes.isErr()) {
            if (carouselContainerStatusRes.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS) {
                return err({
                    endpoint: MetaGraphApiHelperEndpoint.GET_IG_CONTAINER,
                    code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                    stringifiedRawError: JSON.stringify(carouselContainerStatusRes.error, errorReplacer),
                });
            }
            assert(carouselContainerStatusRes.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            const { lastResult } = carouselContainerStatusRes.error;
            if (lastResult.isErr()) {
                return err(lastResult.error);
            }
            return err({
                endpoint: MetaGraphApiHelperEndpoint.GET_IG_CONTAINER,
                code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                stringifiedRawError: JSON.stringify({ max_attempts_reached: lastResult.value }),
            });
        }

        const mediaIdRes = await this._metaGraphApiHelper.publishIgContainer(
            credentialId,
            igUserId,
            carouselContainerIdRes.value.containerId
        );

        if (mediaIdRes.isErr()) {
            return err(mediaIdRes.error);
        }

        return ok({ mediaId: mediaIdRes.value.mediaId });
    }
}
