import { singleton } from 'tsyringe';

import {
    MetaGraphApiCredentialsHandlerErrorCode,
    MetaGraphApiCredentialsHandlerErrorCodes,
} from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';

@singleton()
export class ShouldRetryPublicationService {
    execute(error: MetaGraphApiCredentialsHandlerErrorCode): boolean {
        switch (error) {
            case MetaGraphApiCredentialsHandlerErrorCodes.USER_NEEDS_TO_LOG_IN:
            case MetaGraphApiCredentialsHandlerErrorCodes.USER_NEEDS_TO_LOG_IN_TO_IG_APP:
            case MetaGraphApiCredentialsHandlerErrorCodes.USER_MISSING_APPROPRIATE_ROLE:
            case MetaGraphApiCredentialsHandlerErrorCodes.CREDENTIAL_NOT_FOUND:
            case MetaGraphApiCredentialsHandlerErrorCodes.CREDENTIAL_PAGE_ACCESS_TOKEN_NOT_FOUND:
            case MetaGraphApiCredentialsHandlerErrorCodes.INVALID_TOKEN:
            case MetaGraphApiCredentialsHandlerErrorCodes.INVALID_LOCATION:
            case MetaGraphApiCredentialsHandlerErrorCodes.USER_NOT_VISIBLE:
                return false;
            case MetaGraphApiCredentialsHandlerErrorCodes.MEDIA_PUBLISHED_BEFORE_BUSINESS_ACCOUNT_CONVERSION:
            case MetaGraphApiCredentialsHandlerErrorCodes.CANNOT_VALIDATE_RESPONSE:
            case MetaGraphApiCredentialsHandlerErrorCodes.CANNOT_VALIDATE_ERROR_RESPONSE:
            case MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR:
            case MetaGraphApiCredentialsHandlerErrorCodes.VIDEO_REEL_PUBLISH_WAIT_MAX_ATTEMPTS_EXCEEDED:
            case MetaGraphApiCredentialsHandlerErrorCodes.VIDEO_REEL_UPLOAD_WAIT_MAX_ATTEMPTS_EXCEEDED:
            case MetaGraphApiCredentialsHandlerErrorCodes.MEDIA_CREATION_FAILED:
                return true;
        }
    }
}
