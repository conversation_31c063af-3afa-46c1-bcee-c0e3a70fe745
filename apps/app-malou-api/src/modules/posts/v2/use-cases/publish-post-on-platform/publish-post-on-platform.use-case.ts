import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { inject, singleton } from 'tsyringe';

import { IPopulatedPost, IPost, IPostPublicationError } from '@malou-io/package-models';
import { errorReplacer, PlatformKey, PostPublicationStatus, PublicationErrorCode } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { PublishPostOnFacebookUseCase } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-facebook.use-case';
import { PublishPostOnInstagramUseCase } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-instagram.use-case';
import { PublishPostOnMapstrUseCase } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-mapstr.use-case';
import { PublishPostOnPlatform } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-platform.interface';
import { PublishPostOnTiktokUseCase } from ':modules/posts/v2/use-cases/publish-post-on-platform/platforms/publish-post-on-tiktok.use-case';
import { SlackChannel, SlackService } from ':services/slack.service';

const MAX_TRIES = 5;
const MAX_MINUTES_TO_PUBLISH = 60;

// TODO posts-v2 write a test for this
@singleton()
export class PublishPostOnPlatformUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        @inject(PublishPostOnFacebookUseCase)
        private readonly _publishPostOnFacebookUseCase: PublishPostOnPlatform,
        @inject(PublishPostOnInstagramUseCase)
        private readonly _publishPostOnInstagramUseCase: PublishPostOnPlatform,
        @inject(PublishPostOnTiktokUseCase)
        private readonly _publishPostOnTiktokUseCase: PublishPostOnPlatform,
        @inject(PublishPostOnMapstrUseCase)
        private readonly _publishPostOnMapstrUseCase: PublishPostOnPlatform,
        private readonly _slackService: SlackService,
        private readonly _createPostErrorNotificationProducer: CreatePostErrorNotificationProducer,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute({ postId }: { postId: string }): Promise<void> {
        try {
            logger.info('[PublishPostOnPlatformUseCase] Start', { postId });

            const post = await this._postsRepository.findOne({
                filter: { _id: postId },
                options: { populate: [{ path: 'attachments' }, { path: 'thumbnail' }, { path: 'feedback' }], lean: true },
            });

            if (!post) {
                logger.info('[PublishPostOnPlatformUseCase] Post does not exists', { postId });
                return;
            }

            logger.info('[PublishPostOnPlatformUseCase] Post info', { attachments: post.attachments, published: post.published });
            assert(post.attachments.length, 'Post does not have attachments');
            assert(post.published === PostPublicationStatus.PENDING, 'Post is not pending');

            // TODO posts-v2 nice to have, check if post exist in fb via the api

            const similarPost = await this._fetchSimilarPost(post);
            if (similarPost) {
                logger.info('[PublishPostOnPlatformUseCase] Similar post already exists', {
                    postId,
                    similarPostId: similarPost._id.toString(),
                });
                // Save the social data of the similar post in the current post after deleting the similar post
                await this._postsRepository.deleteOne({ filter: { _id: similarPost._id } });
                await this._saveSocialDataInCurrentPost({ post, similarPost });
                return;
            }

            if ((post.tries ?? 0) >= MAX_TRIES) {
                const metadata = { tries: post.tries, MAX_TRIES };
                await this._setPostInError(postId, '[PublishPostOnPlatformUseCase] Max tries reached', metadata, undefined, false);
                const lastError = post.publicationErrors?.at(-1);
                this._slackService.sendAlert({
                    data: { err: new Error(`Max tries reached (${lastError?.code ?? '--'})`), metadata: { lastError } },
                    channel: SlackChannel.POSTS_V2_ALERTS,
                    shouldPing: true,
                });
                return;
            }

            await this._postsRepository.incrementTries(post._id.toString());

            assert(post.plannedPublicationDate, 'Post does not have a planned publication date');
            const maxPublicationDate = DateTime.fromJSDate(post.plannedPublicationDate)
                .plus({ minutes: MAX_MINUTES_TO_PUBLISH })
                .toJSDate();
            if (Date.now() > maxPublicationDate.getTime()) {
                const metadata = { plannedPublicationDate: post.plannedPublicationDate, MAX_MINUTES_TO_PUBLISH };
                await this._setPostInError(postId, '[PublishPostOnPlatformUseCase] Too late to publish', metadata);
                return;
            }

            assert(post.platformId, 'Post does not have a platformId');
            const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());
            const credentialId = platform?.credentials?.[0];
            if (!credentialId) {
                const metadata = {
                    postPlatformId: post.platformId.toString(),
                    platformWasFound: !!platform,
                    credentials: platform?.credentials,
                };
                await this._setPostInError(postId, '[PublishPostOnPlatformUseCase] Platform or credential not found', metadata, {
                    code: PublicationErrorCode.CONNECTION_EXPIRED,
                });
                return;
            }

            const publishPostOnPlatformUseCases: Partial<Record<PlatformKey, PublishPostOnPlatform>> = {
                [PlatformKey.FACEBOOK]: this._publishPostOnFacebookUseCase,
                [PlatformKey.INSTAGRAM]: this._publishPostOnInstagramUseCase,
                [PlatformKey.TIKTOK]: this._publishPostOnTiktokUseCase,
                [PlatformKey.MAPSTR]: this._publishPostOnMapstrUseCase,
            };
            assert(post.key, 'Post does not have a key');
            const useCase = publishPostOnPlatformUseCases[post.key];
            assert(useCase, `publishPostOnPlatformUseCaseUseCase is not implemented for ${post.key}`);

            await useCase.execute(post, platform, credentialId);

            logger.info('[PublishPostOnPlatformUseCase] End');
        } catch (err) {
            await this._setPostInError(postId, '[PublishPostOnPlatformUseCase] Unexpected thrown error', err);

            this._slackService.sendAlert({ data: { err }, channel: SlackChannel.POSTS_V2_ALERTS });

            throw err;
        }
    }

    private async _setPostInError(
        postId: string,
        logMessage: string,
        logMetadata: any,
        publicationErrorOverride?: Partial<IPostPublicationError>,
        // When the error is MAX_TRIES_REACHED, we don't update the retro compatibility fields
        // so the calendar, which relies on these old fields, shows the last error, which displays a more informative error message.
        shouldUpdateRetroCompatibilityFields = true
    ): Promise<void> {
        logger.error(logMessage, { logMetadata });
        const stringifiedError = `${logMessage} ${JSON.stringify(logMetadata, errorReplacer)}`;
        await this._postsRepository.updatePublicationStatus(postId, PostPublicationStatus.ERROR);
        const publicationErrorComputed = { happenedAt: new Date(), data: stringifiedError, ...publicationErrorOverride };
        await this._postsRepository.pushPublicationError(postId, publicationErrorComputed, shouldUpdateRetroCompatibilityFields);
        await this._createPostErrorNotificationProducer.execute({ postId });
    }

    private async _fetchSimilarPost(post: IPopulatedPost): Promise<IPost | null> {
        const twoWeeksAgo = DateTime.now().minus({ weeks: 2 }).toJSDate();
        const alreadyExistingPost = await this._postsRepository.findOne({
            filter: {
                _id: { $ne: post._id },
                platformId: post.platformId,
                text: post.text,
                published: PostPublicationStatus.PUBLISHED,
                socialId: { $ne: null },
                socialCreatedAt: { $ne: null, $gt: twoWeeksAgo },
            },
            options: { lean: true },
        });
        return alreadyExistingPost;
    }

    private async _saveSocialDataInCurrentPost({ post, similarPost }): Promise<void> {
        await this._postsRepository.updateOne({
            filter: { _id: post._id },
            update: {
                published: PostPublicationStatus.PUBLISHED,
                socialId: similarPost.socialId,
                socialCreatedAt: similarPost.socialCreatedAt,
                socialUpdatedAt: similarPost.socialUpdatedAt,
                socialLink: similarPost.socialLink,
                text: similarPost.text,
                hashtags: similarPost.hashtags,
                socialAttachments: similarPost.socialAttachments,
                isPublishing: false,
            },
        });
    }
}
