import { singleton } from 'tsyringe';

import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';

@singleton()
export class RemoveInstagramCollaboratorsAndTagsUseCase {
    constructor(private readonly _postsRepository: PostsRepository) {}

    async execute(postId: string): Promise<void> {
        await this._postsRepository.removeInstagramCollaboratorsAndTags(postId);
    }
}
