import { singleton } from 'tsyringe';

import {
    AiInteractionRelatedEntityCollection,
    GenerateStoreLocatorContentType,
    MalouErrorCode,
    StoreLocatorFaqBlockQuestionType,
    StoreLocatorLanguage,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { GenericAiService, GenericAiServiceResponseType } from ':microservices/ai-lambda-template/generic-ai-service';

export interface GenerateStoreLocatorContentPayload<E = AiInteractionRelatedEntityCollection.STORE_LOCATOR_RESTAURANT_PAGE, T = string> {
    relatedEntityCollection: E;
    type: GenerateStoreLocatorContentType;
    restaurantData: {
        /**
         * The name of the restaurant. Optional if the organizationName is sufficient for content generation (centralization pages).,
         */
        restaurantName?: string;
        organizationName: string;
        address: {
            locality?: string;
            postalCode?: string;
            formattedAddress?: string;
            isNotUniqueAddress?: boolean;
        };
        language: StoreLocatorLanguage;
        keywords: string[];
        bricks: string[];
        brandTone?: string[];
        targetAudience?: string[];
        specificsDirectives?: string[];
        restaurantOffers?: string[];
        restaurantContext?: string[];
        questionsTemplate?: StoreLocatorFaqBlockQuestionType[];
        context?: Partial<Record<GenerateStoreLocatorContentType, string>>[];
        previousGeneration?: T;
    };
}

export interface AiStoreLocatorContent {
    text: string;
}

export interface AiStoreLocatorDescriptionsContent {
    blocks: {
        title: string;
        sections: {
            subtitle: string;
            text: string;
        }[];
    }[];
}

export interface AiStoreLocatorFaqContent {
    faqs: {
        question: string;
        answer: string;
    }[];
}

export interface AiStoreLocatorMapKeywordsContent {
    keywords: string[];
}

export type AiStoreLocatorContentType<T> = T extends GenerateStoreLocatorContentType.DESCRIPTION_BLOCK_GENERATION
    ? AiStoreLocatorDescriptionsContent
    : T extends GenerateStoreLocatorContentType.FAQ_BLOCK_GENERATION
      ? AiStoreLocatorFaqContent
      : T extends GenerateStoreLocatorContentType.MAP_KEYWORDS_GENERATION
        ? AiStoreLocatorMapKeywordsContent
        : AiStoreLocatorContent;

@singleton()
export class AiStoreLocatorContentService {
    private readonly _MAX_RETRIES_COUNT = 3;

    async generateStoreLocatorContent<T extends GenerateStoreLocatorContentType>(
        type: T,
        payload: GenerateStoreLocatorContentPayload['restaurantData'],
        retryCount?: number
    ): Promise<GenericAiServiceResponseType<AiStoreLocatorContentType<T>>> {
        try {
            const AiService = new GenericAiService<GenerateStoreLocatorContentPayload, AiStoreLocatorContentType<T>>({
                lambdaUrl: Config.services.aiStoreLocatorContentGenerationService.functionName,
            });
            return await AiService.generateCompletion({
                restaurantData: payload,
                relatedEntityCollection: AiInteractionRelatedEntityCollection.STORE_LOCATOR_RESTAURANT_PAGE,
                type,
            });
        } catch (error) {
            const retriesCount = retryCount ?? 0;
            const shouldRetry =
                retriesCount < this._MAX_RETRIES_COUNT &&
                error instanceof MalouError &&
                error.malouErrorCode === MalouErrorCode.AI_REQUEST_FAILED;

            if (shouldRetry) {
                return await this.generateStoreLocatorContent(type, payload, retriesCount + 1);
            }

            throw error;
        }
    }
}
