---
import type { StoreLocatorLanguage } from ':interfaces/pages.interfaces';
import type { GetStoreLocatorPagesDto } from '@malou-io/package-dto/src/store-locator/store-locator.response.dto';
import { Picture } from 'astro:assets';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    stores: {
        name: string;
        relativePath: string;
    }[];
    urls: NonNullable<GetStoreLocatorPagesDto['urls']>;
    lang: StoreLocatorLanguage;
}

const { stores, urls, lang } = Astro.props;

const mapUrl = urls[lang]?.map ?? '/';

const links = [
    {
        title: 'Accueil',
        url: 'https://gardenicecafe.com/',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/accueil-garden-ice-cafe.png',
    },
    {
        title: 'La carte',
        url: 'https://gardenicecafe.com/la-carte',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/carte-garden-ice-cafe.png',
    },
    {
        title: 'Nos Restaurants',
        url: '/' + mapUrl,
        dataSrc: null,
    },
    {
        title: 'Club Privilège',
        url: 'https://gardenicecafe.com/carte-club-privilege',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/privilege-garden-ice-cafe.png',
    },
    {
        title: 'Contact',
        url: 'https://gardenicecafe.com/contact',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/contact-garden-ice-cafe.png',
    },
    {
        title: 'Emploi',
        url: 'https://gardenicecafe.com/emploi',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/emploi-garden-ice-cafe.png',
    },
    {
        title: 'Franchise',
        url: 'https://gardenicecafe.com/devenez-franchise',
        dataSrc:
            'https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/franchise-garden-ice-cafe.png',
    },
];
---

<style>
    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .dynamic-item {
        animation: slideDown 0.3s ease-out;
    }
</style>
<header
    class="bg-primary font-secondary px-xl-4 fixed top-0 left-0 z-100 w-full px-5 py-6"
>
    <div class="relative flex h-full items-start">
        <div class="flex w-1/3 justify-start">
            <button id="mobile-menu-button" aria-label="Toggle Menu">
                <svg
                    class="stroke-fourth h-9 w-9"
                    xmlns="http://www.w3.org/2000/svg"
                    ><g>
                        <path
                            d="M4 6H24M4 15H24M4 24H12"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"></path>
                    </g></svg
                >
            </button>
        </div>
        <div class="flex w-1/3 items-center justify-center">
            <div
                class="flex max-h-max max-w-max items-center justify-center sm:right-0"
            >
                <a
                    href="https://gardenicecafe.com/"
                    aria-label="Home Page"
                    class="block"
                >
                    <Picture
                        src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6536a39cb6b3ebbdec97fced/favicons/logo.webp"
                        formats={['png']}
                        fallbackFormat="png"
                        alt="Logo Garden Ice Cafe"
                        inferSize
                        class="h-[50px] w-[150px]"
                        densities={[1, 2, 3]}
                    />
                </a>
            </div>
        </div>
        <div class="hidden w-1/3 justify-end md:flex">
            <div
                id="open-popup-uber-eats"
                class="border-secondary hover:bg-secondary text-fourth mr-4 flex min-w-fit cursor-pointer items-center justify-center rounded-full border bg-none px-7 py-3 hover:text-black"
            >
                <div
                    class="font-secondary text-[16px] font-normal text-inherit uppercase"
                >
                    Commander en ligne
                </div>
            </div>
        </div>
    </div>

    <div
        id="mobile-menu"
        class="bg-primary fixed top-0 right-0 z-40 flex h-full w-full -translate-y-full transform flex-col overflow-auto shadow-lg transition-transform duration-600 ease-in-out md:bg-[#1b1511]"
    >
        <div class="relative flex w-full">
            <div class="bg-primary flex w-1/2 justify-start">
                <button
                    id="close-menu"
                    aria-label="Close menu"
                    class="mt-8 ml-5 self-start"
                >
                    <svg
                        viewBox="0 0 16 16"
                        xmlns="http://www.w3.org/2000/svg"
                        class="fill-fourth h-6 w-6"
                    >
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M13.5303 2.46967C13.8232 2.76256 13.8232 3.23744 13.5303 3.53033L3.53033 13.5303C3.23744 13.8232 2.76256 13.8232 2.46967 13.5303C2.17678 13.2374 2.17678 12.7626 2.46967 12.4697L12.4697 2.46967C12.7626 2.17678 13.2374 2.17678 13.5303 2.46967Z"
                        ></path>
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M13.5303 13.5303C13.2374 13.8232 12.7626 13.8232 12.4697 13.5303L2.46967 3.53033C2.17678 3.23744 2.17678 2.76256 2.46967 2.46967C2.76256 2.17678 3.23744 2.17678 3.53033 2.46967L13.5303 12.4697C13.8232 12.7626 13.8232 13.2374 13.5303 13.5303Z"
                        ></path>
                    </svg>
                </button>
            </div>
            <div
                class="pointer-events-none absolute mt-6 flex w-full justify-center"
            >
                <div
                    class="flex max-h-max max-w-max items-center justify-center sm:right-0"
                >
                    <a
                        href="https://gardenicecafe.com/"
                        aria-label="Home Page"
                        class="block"
                    >
                        <Picture
                            src="https://malou-production.s3.eu-west-3.amazonaws.com/store-locator/organization/6536a39cb6b3ebbdec97fced/favicons/logo.webp"
                            formats={['png']}
                            fallbackFormat="png"
                            alt="Logo Garden Ice Cafe"
                            inferSize
                            class="h-[50px] w-[150px]"
                            densities={[1, 2, 3]}
                        />
                    </a>
                </div>
            </div>
            <div class="hidden w-1/2 justify-end md:flex">
                <div
                    id="open-popup-uber-eats-header"
                    class="border-secondary hover:bg-secondary text-fourth mt-6.5 mr-8 flex min-w-fit cursor-pointer items-center justify-center rounded-full border bg-none px-7 py-3 hover:text-black"
                >
                    <a
                        class="font-secondary text-[16px] font-normal text-inherit uppercase"
                        >Commander en ligne</a
                    >
                </div>
            </div>
        </div>

        <div class="flex h-full w-full">
            <div
                class="bg-primary flex h-full w-full flex-col items-center justify-center md:w-1/2"
            >
                <div class="p-6 pt-0">
                    <div
                        class="flex flex-col items-center gap-10 md:items-start"
                    >
                        {
                            links.map((link, index) => (
                                <a
                                    href={link.url}
                                    data-item={index}
                                    data-src={link.dataSrc}
                                    class="item text-fourth hover:text-secondary font-secondary text-[20px] font-bold uppercase md:text-[26px]"
                                >
                                    {link.title}
                                </a>
                            ))
                        }
                        <div
                            class="flex w-full items-center justify-center md:hidden"
                        >
                            <div
                                id="open-popup-uber-eats-header-2"
                                class="border-secondary hover:bg-secondary text-fourth flex min-w-fit cursor-pointer items-center justify-center rounded-full border bg-none px-7 py-3 hover:text-black"
                            >
                                <a
                                    class="font-secondary text-[16px] font-normal text-inherit uppercase"
                                    >Commander en ligne</a
                                >
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="relative hidden w-1/2 flex-col px-2 pt-3 md:flex"
                id="dynamic-content"
            >
                <div class="flex w-full items-center justify-center px-2">
                    {
                        links.map(
                            (link, index) =>
                                link.dataSrc && (
                                    <Picture
                                        id={`dynamic-item-${index}`}
                                        src={link.dataSrc!}
                                        formats={['png']}
                                        fallbackFormat="png"
                                        alt="image"
                                        inferSize
                                        class="dynamic-item hidden w-[450px]"
                                        densities={[1, 2, 3]}
                                    />
                                ),
                        )
                    }
                </div>
                <div id="restaurants-list" class="mt-36 hidden">
                    <div class="flex flex-col items-center gap-5">
                        {
                            stores.map((store) => (
                                <a
                                    class="font-tertiary text-fourth text-[27px] font-normal italic"
                                    href={`/` + store.relativePath}
                                >
                                    {store.name}
                                </a>
                            ))
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div
        id="popup-uber-eats"
        class="fixed inset-0 z-9999 hidden items-center justify-center bg-black/70 shadow-lg"
    >
        <div
            class="bg-primary flex h-[70%] w-[80%] flex-col items-center rounded-[50px] px-8 py-7 lg:w-[70%]"
        >
            <div
                class="flex w-full cursor-pointer justify-end"
                id="close-popup-uber-eats"
            >
                <picture data-loader="pic">
                    <source
                        srcset="https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/close.webp"
                        type="image/webp"
                    />
                    <source
                        srcset="https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/close.png"
                        type="image/png"
                    />
                    <img
                        src="https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/close.png"
                        class="img-fluid b-close"
                    />
                </picture>
            </div>

            <div class="mt-6 flex flex-col items-center">
                <Picture
                    src="https://mediab.izipass.cloud/GardenIceCafeRefonte/Content/images/accueil-general/ubereats.png"
                    formats={['png']}
                    fallbackFormat="png"
                    alt="Logo Garden Ice Cafe"
                    inferSize
                    class="mb-6 h-[82px] w-[180px]"
                    densities={[1, 2, 3]}
                />

                <p
                    class="text-fourth mb-2 text-[18px] font-normal uppercase md:text-[27px]"
                >
                    SÉLECTIONNEZ VOTRE RESTAURANT
                </p>
                <p
                    class="text-fourth mb-2 text-[15px] font-normal uppercase md:text-[18px]"
                >
                    POUR COMMANDER EN LIVRAISON
                </p>
                <div class="mt-12 flex w-full flex-col items-center">
                    <select
                        id="restaurant"
                        class="mb-4 h-16 w-full rounded-full border border-[#c0944a] bg-white pl-5 uppercase shadow-sm md:w-[500px]"
                        required=""
                    >
                        <option
                            value="https://www.ubereats.com/fr/store/garden-ice-cafe-brive-la-gaillarde/AmC7k1m6RHCDV9UKoGkRqw"
                            >Brive-la-Gaillarde</option
                        >
                        <option
                            value="https://www.ubereats.com/fr/store/garden-ice-cafe-cognac/dOFlXCWQSfqOQhIsb3mwXw"
                            >Cognac</option
                        >
                        <option
                            value="https://www.ubereats.com/fr/store/garden-ice-cafe-orleans/dlg2E3g-T7Oa6Uo1J5pJnQ?utm_source=AdWords_NonBrand&utm_campaign=*********-search-google-nonbrand_61_-99_FR-National_e_all_acq_cpc_en_SMB_DSA_Exact__dsa-2055708369259_654976966062_147645554786__c&campaign_id=19977438689&adg_id=147645554786&fi_id=82948598585&match=&net=g&dev=c&dev_m=&ad_id=654976966062&cre=654976966062&kwid=dsa-2055708369259&kw=&placement=&tar=&&&&&gclsrc=aw.ds&gclid=EAIaIQobChMIx6Oc5KvU_wIVZJRoCR3ZYQBtEAAYASAAEgK_MPD_BwE"
                            >Orléans</option
                        >
                        <option
                            value="https://www.ubereats.com/fr/store/garden-ice-cafe-perigueux/JZBxmfD1QTGm61TbEbKlyQ?utm_source=AdWords_NonBrand&utm_campaign=*********-search-google-nonbrand_61_-99_FR-National_e_all_acq_cpc_en_SMB_DSA_Exact__dsa-2055708369259_654976966062_147645554786__c&campaign_id=19977438689&adg_id=147645554786&fi_id=82991171445&match=&net=g&dev=c&dev_m=&ad_id=654976966062&cre=654976966062&kwid=dsa-2055708369259&kw=&placement=&tar=&&&&&gclsrc=aw.ds&gclid=EAIaIQobChMIws7u7KvU_wIVMSwGAB3jWgIAEAAYASAAEgIlpfD_BwE"
                            >Périgueux</option
                        >
                    </select>
                    <div
                        id="order-button"
                        class="bg-secondary mb-4 flex h-16 w-full cursor-pointer items-center justify-center rounded-full border border-[#c0944a] p-2 text-[17px] uppercase md:w-[500px]"
                    >
                        Commander
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
    const menu = document.getElementById('mobile-menu');
    const openBtn = document.getElementById('mobile-menu-button');
    const closeBtn = document.getElementById('close-menu');

    const items = document.querySelectorAll('.item');
    const dynamicItems = document.querySelectorAll('.dynamic-item');
    const restaurantsList = document.getElementById('restaurants-list');

    const openPopupUberEatsBtn = document.getElementById(
        'open-popup-uber-eats',
    );
    const openPopupUberEatsHeaderBtn = document.getElementById(
        'open-popup-uber-eats-header',
    );
    const openPopupUberEatsHeader2Btn = document.getElementById(
        'open-popup-uber-eats-header-2',
    );

    const openPopupUberEatsFooterBtn = document.getElementById(
        'order-button-footer',
    );
    const popupUberEats = document.getElementById('popup-uber-eats');
    const closePopupUberEatsBtn = document.getElementById(
        'close-popup-uber-eats',
    );

    const orderButton = document.getElementById('order-button');

    function openMenu() {
        menu?.classList.remove('-translate-y-full');
    }

    function closeMenu() {
        menu?.classList.add('-translate-y-full');
    }

    openBtn?.addEventListener('click', (e) => {
        e.stopPropagation();
        openMenu();
    });

    closeBtn?.addEventListener('click', (e) => {
        e.stopPropagation();
        closeMenu();
    });

    items.forEach((item) => {
        const el = item as HTMLElement;
        el.addEventListener('mouseenter', () => {
            if (el.dataset.src) {
                dynamicItems.forEach((di) => di.classList.add('hidden'));
                restaurantsList?.classList.add('hidden');
                const index = el.dataset.item;
                const dynamicItem = document.getElementById(
                    `dynamic-item-${index}`,
                );
                dynamicItem?.classList.remove('hidden');
            } else if (!el.dataset.type) {
                dynamicItems.forEach((di) => di.classList.add('hidden'));
                restaurantsList?.classList.toggle('hidden');
            }
        });
    });

    function togglePopupUberEats() {
        popupUberEats?.classList.toggle('hidden');
        popupUberEats?.classList.toggle('flex');
    }

    [
        openPopupUberEatsBtn,
        openPopupUberEatsHeaderBtn,
        openPopupUberEatsHeader2Btn,
        openPopupUberEatsFooterBtn,
    ].forEach((btn) => {
        btn?.addEventListener('click', togglePopupUberEats);
    });

    closePopupUberEatsBtn?.addEventListener('click', () => {
        popupUberEats?.classList.toggle('hidden');
        popupUberEats?.classList.toggle('flex');
    });

    orderButton?.addEventListener('click', () => {
        const restaurantSelect = document.getElementById(
            'restaurant',
        ) as HTMLSelectElement;
        const selectedUrl = restaurantSelect.value;
        console.log('selectedUrl', selectedUrl);

        if (selectedUrl) {
            window.open(selectedUrl, '_blank');
        }
    });
</script>
