---
import PinIcon from ':assets/icons/pin.svg';
import PhoneIcon from ':assets/icons/phone.svg';
import type { GetStoreLocatorCentralizationPageDto } from '@malou-io/package-dto';
import { generateMapImages } from ':utils/images/mapImages';
import { getStyles } from ':utils/get-element-styles';
import { initTranslationFunction } from ':i18n/index';
interface Props {
    stores: GetStoreLocatorCentralizationPageDto['stores'];
    styles: GetStoreLocatorCentralizationPageDto['styles'];
    mapComponents: GetStoreLocatorCentralizationPageDto['mapComponents'];
}

const { stores, mapComponents, styles } = Astro.props as Props;

const pins = mapComponents.pins;

const {
    activeMarkerIcon,
    inactiveMarkerIcon,
    stores: transformedStores,
} = await generateMapImages({
    pins,
    popupData: mapComponents.popup,
    stores,
});

// Leaflet Map Configuration

const getElementStyles = getStyles({ styles });
const t = await initTranslationFunction();

const openSoonText = t('information.opening-soon');
const moreDetailsText = t('map.more-details');

const mapConfig = {
    stores: transformedStores,
    icons: {
        activeMarkerIcon,
        inactiveMarkerIcon,
        pinIcon: PinIcon,
        phoneIcon: PhoneIcon.src,
    },
    translation: {
        openSoonText,
        moreDetailsText,
    },
    styles: {
        popupWrapperStyle: getElementStyles({
            elementId: 'map-popup-wrapper',
        }),
        popupTitleStyle: getElementStyles({
            elementId: 'map-popup-title',
        }),
        popupStoreNotOpenedYetStyle: getElementStyles({
            elementId: 'map-page-store-not-open-yet',
        }),
        popupIconsStyle: getElementStyles({
            elementId: 'map-page-icons',
        }),
        markerGroupStyle: getElementStyles({
            elementId: 'map-marker-group',
        }),
    },
} as const;
---

<!-- Global map data for client-side scripts -->
<script is:inline define:vars={{ mapConfig }}>
    function getMapConfig() {
        return mapConfig;
    }
    window.getMapConfig = getMapConfig;
</script>

<style>
    .ripple-loader {
        position: absolute;
        width: 250px;
        height: 250px;
        background: rgba(0, 123, 255, 0.4);
        border-radius: 50%;
        animation: ripple-loader 1.8s infinite ease-out;
        opacity: 0;
    }

    /* Stagger delays for multiple waves */
    .ripple-loader:nth-child(1) {
        animation-delay: 0.7s;
        opacity: 0;
    }
    .ripple-loader:nth-child(2) {
        animation-delay: 1.2s;
        opacity: 0;
    }
    .ripple-loader:nth-child(3) {
        animation-delay: 1.8s;
        opacity: 0;
    }

    @keyframes ripple-loader {
        0% {
            transform: scale(0.3);
            opacity: 1;
        }
        100% {
            transform: scale(3);
            opacity: 0;
        }
    }

    #zoom-warning.visible {
        opacity: 1;
    }
</style>

<div id="map-wrapper" class="relative flex h-[240px] w-full sm:h-[90vh]">
    <div
        id="map-loader"
        class="absolute z-1000 flex h-[240px] w-full items-center justify-center bg-white sm:h-[90vh]"
    >
        <img
            src={activeMarkerIcon.src}
            srcset={activeMarkerIcon.srcSet}
            alt={activeMarkerIcon.alt}
            {...activeMarkerIcon.attributes}
            class="!h-10 !w-10 rounded-full object-cover"
        />
        <div class="ripple-loader"></div>
        <div class="ripple-loader"></div>
        <div class="ripple-loader"></div>
    </div>

    <div
        id="zoom-warning"
        class="pointer-events-none absolute top-0 left-0 z-1000 h-[240px] w-full bg-black/65 p-4 text-center text-lg text-white opacity-0 transition-opacity duration-400 ease-in-out sm:h-[90vh]"
    >
        <p class="flex h-full w-full items-center justify-center">
            {t('map.scroll-warning')}
        </p>
    </div>
    <div id="map" class="z-1 h-[240px] w-full sm:h-[90vh]"></div>
</div>

<script src="./../../scripts/map/leaflet-init.ts"></script>
