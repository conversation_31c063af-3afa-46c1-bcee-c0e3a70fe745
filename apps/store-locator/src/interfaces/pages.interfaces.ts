import type { Day } from ':constants/enum';

import type {
    GetStoreLocatorCentralizationPageDto,
    GetStoreLocatorPagesDto,
    GetStoreLocatorStorePageDto,
} from '@malou-io/package-dto';

export enum PageType {
    STORE = 'store',
    MAP = 'map',
}

export type StoreLocatorLanguage =
    GetStoreLocatorPagesDto['mapPages'][0]['lang'];

export type StaticLinks = Partial<
    Record<StoreLocatorLanguage, { text: string; href: string }[]>
>;

export type IStorePage = GetStoreLocatorStorePageDto & {
    headBlock: GetStoreLocatorStorePageDto['headBlock'] & {};
};

export interface Store {
    name: string;
    relativePath: string;
}

export interface PageData {
    params: {
        path: string;
    };
    props: {
        data: IStorePage | GetStoreLocatorCentralizationPageDto;
        urls: GetStoreLocatorPagesDto['urls'];
        stores: Store[];
        type: PageType;
    };
}

export interface TransformedImageProps {
    src: string;
    srcSet: string;
    attributes: Record<string, string>;
    alt: string;
}

export interface IMapConfig {
    stores: (GetStoreLocatorCentralizationPageDto['stores'][number] & {
        imageProps: TransformedImageProps;
    })[];
    icons: {
        activeMarkerIcon: TransformedImageProps;
        inactiveMarkerIcon: TransformedImageProps;
        pinIcon: string;
        phoneIcon: string;
    };
    translation: {
        openSoonText: string;
        moreDetailsText: string;
    };
    styles: {
        popupWrapperStyle: string;
        popupTitleStyle: string;
        popupStoreNotOpenedYetStyle: string;
        popupIconsStyle: string;
        markerGroupStyle: string;
    };
}

export interface IMapSearchData {
    id: string;
    coordinates: {
        lat: number;
        lng: number;
    };
}

// Information block interfaces
export interface InformationBlockTranslation {
    CLOSED: string;
    OPEN_NOW: string;
    OPEN_SOON: string;
    DAYS: Record<Day, string>;
}

export interface InformationBlockExtraStyles {
    hoursHighlight: string;
}
