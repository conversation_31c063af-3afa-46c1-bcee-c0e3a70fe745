import type {
    IMapConfig,
    TransformedImageProps,
} from ':interfaces/pages.interfaces';
import type { GetImageResult } from 'astro';
import { getImage } from 'astro:assets';

import type { GetStoreLocatorCentralizationPageDto } from '@malou-io/package-dto';

export async function generateMapImages({
    pins,
    popupData,
    stores,
}: {
    pins: GetStoreLocatorCentralizationPageDto['mapComponents']['pins'];
    popupData: GetStoreLocatorCentralizationPageDto['mapComponents']['popup'];
    stores: GetStoreLocatorCentralizationPageDto['stores'];
}): Promise<{
    activeMarkerIcon: TransformedImageProps;
    inactiveMarkerIcon: TransformedImageProps;
    stores: IMapConfig['stores'];
}> {
    let noStoreImageTransformed = null;
    if (popupData?.noStoreImage) {
        noStoreImageTransformed = await getImage({
            src: popupData.noStoreImage.url,
            formats: ['webp'],
            fallbackFormats: ['jpg'],
            inferSize: true,
            height: 140,
            width: 260,
            densities: [1, 2, 3],
        });
    }

    const { activePin, inactivePin } = pins;

    const [activePinImage, transformedStores] = await Promise.all([
        getImage({
            src: activePin.url,
            formats: ['webp'],
            fallbackFormats: ['jpg'],
            height: 40,
            width: 40,
            densities: [1, 2, 3],
        }),
        transformStoresImages(
            stores,
            noStoreImageTransformed
                ? {
                      ...noStoreImageTransformed,
                      alt: popupData?.noStoreImage?.description || '',
                  }
                : null,
        ),
    ]);

    // in case there is no inactive pin, we use the active pin as the inactive pin
    const inactivePinImage = inactivePin
        ? await getImage({
              src: inactivePin.url,
              formats: ['webp'],
              fallbackFormats: ['jpg'],
              height: 40,
              width: 40,
              densities: [1, 2, 3],
          })
        : activePinImage;

    const {
        fallbackFormat: activeMarkerIconFallbackFormat,
        ...activeMarkerIconAttributes
    } = activePinImage.attributes;
    const {
        fallbackFormat: inactiveMarkerIconFallbackFormat,
        ...inactiveMarkerIconAttributes
    } = inactivePinImage.attributes;

    return {
        activeMarkerIcon: {
            src: activePinImage.src,
            srcSet: activePinImage.srcSet.attribute,
            attributes: activeMarkerIconAttributes,
            alt: activePin.description,
        },
        inactiveMarkerIcon: {
            src: inactivePinImage.src,
            srcSet: inactivePinImage.srcSet.attribute,
            attributes: inactiveMarkerIconAttributes,
            alt: inactivePin ? inactivePin.description : activePin.description,
        },
        stores: transformedStores,
    };
}

async function transformStoresImages(
    stores: GetStoreLocatorCentralizationPageDto['stores'],
    noStoreImage: (GetImageResult & { alt: string }) | null,
): Promise<IMapConfig['stores']> {
    if (!noStoreImage) {
        return Promise.all(
            stores.map(async (store) => {
                const transformedImageProps = await transformRestaurantImage(
                    store.image,
                );
                return {
                    ...store,
                    imageProps: transformedImageProps,
                };
            }),
        );
    }

    const { fallbackFormat: noStoreFallbackFormat, ...noStoreAttributes } =
        noStoreImage.attributes;

    return Promise.all(
        stores.map(async (store) => {
            const restaurantImageProps = store.isNotOpenedYet
                ? {
                      src: noStoreImage.src,
                      srcSet: noStoreImage.srcSet.attribute,
                      attributes: noStoreAttributes,
                      alt: noStoreImage.alt,
                  }
                : await transformRestaurantImage(store.image);
            return {
                ...store,
                imageProps: restaurantImageProps,
            };
        }),
    );
}

async function transformRestaurantImage(
    storeImage: GetStoreLocatorCentralizationPageDto['stores'][number]['image'],
): Promise<TransformedImageProps> {
    const restaurantImageProps = await getImage({
        src: storeImage.url,
        formats: ['webp'],
        fallbackFormat: 'jpg',
        inferSize: true,
        height: 140,
        width: 260,
        densities: [1, 2, 3],
    }).then((image) => {
        const { fallbackFormat, ...attributes } = image.attributes;
        return {
            src: image.src,
            srcSet: image.srcSet.attribute,
            attributes,
            alt: storeImage.description,
        };
    });
    return restaurantImageProps;
}
