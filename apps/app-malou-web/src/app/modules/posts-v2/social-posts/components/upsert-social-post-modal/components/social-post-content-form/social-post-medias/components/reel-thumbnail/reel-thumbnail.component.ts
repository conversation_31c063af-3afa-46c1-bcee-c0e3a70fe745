import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, input, output, signal } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { lastValueFrom } from 'rxjs';

import { MediaType, MimeType, PlatformKey, PublicationType } from '@malou-io/package-utils';

import { MalouSpinnerComponent } from ':core/components/spinner/spinner/malou-spinner.component';
import { RestaurantsService } from ':core/services/restaurants.service';
import { MediaPickerModalComponent } from ':modules/media/media-picker-modal/media-picker-modal.component';
import { MediaPickerFilter } from ':modules/media/media-picker-modal/media-picker-modal.interface';
import { VideoThumbnailSliderComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/components/reel-thumbnail/video-thumbnail-slider/video-thumbnail-slider.component';
import { EditionMedia } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/edition-media.interface';
import { MediaUploaderService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/media-uploader.service';
import { UpsertSocialPostContext } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/contexts/upsert-social-post.context';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { extractSpacedThumbnails } from ':shared/helpers/video-cover-url';
import { Media } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CustomDialogService } from ':shared/services/custom-dialog.service';

@Component({
    selector: 'app-reel-thumbnail',
    templateUrl: './reel-thumbnail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [
        NgClass,
        TranslateModule,
        MatIcon,
        PlatformLogoComponent,
        NgTemplateOutlet,
        VideoThumbnailSliderComponent,
        MalouSpinnerComponent,
    ],
    providers: [MediaUploaderService],
})
export class ReelThumbnailComponent {
    private readonly _upsertSocialPostContext = inject(UpsertSocialPostContext);
    private readonly _mediaUploaderService = inject(MediaUploaderService);
    private readonly _customDialogService = inject(CustomDialogService);
    private readonly _restaurantsService = inject(RestaurantsService);

    /** Must be a video (with type set to MediaType.PHOTO) */
    public readonly media = input.required<EditionMedia & { type: MediaType.VIDEO }>();

    // public readonly isLoading = input.required<boolean>();
    readonly isReadonly = input<boolean>(false);

    /**
     * This output signal emits an object after the user has moved the slider
     */
    public readonly thumbnailSelectedFromVideo = output<{
        /** The time of the frame to capture as a floating point number between 0 (begin) and 1 (end) */
        timeFloat: number;

        /** Same value but in milliseconds */
        thumbnailOffsetTimeInMs: number;
    }>();

    readonly reelThumbnailFromMedia = this._upsertSocialPostContext.upsertSocialPostState.post.reelThumbnailFromMedia;
    readonly reelThumbnailFromFrame = this._upsertSocialPostContext.upsertSocialPostState.post.reelThumbnailFromFrame;

    readonly timelinePreviewUrls = signal<string[]>([]);

    /** between 0 and 1 */
    readonly timelineLoadingProgress = signal<number | undefined>(undefined);

    readonly sliderThumbnailUrl = computed((): string | undefined => {
        const inputThumbnail = this.reelThumbnailFromFrame();
        if (inputThumbnail?.media?.thumbnail1024OutsideUrl) {
            return inputThumbnail?.media?.thumbnail1024OutsideUrl;
        }
    });

    readonly previousSliderThumbnailUrl = signal<string | undefined>(undefined);

    readonly thumbnailCursorPosition = computed((): number | undefined => {
        const thumbnail = this.reelThumbnailFromFrame();
        const videoDurationSeconds = this._videoDurationSeconds();
        if (!thumbnail) {
            return undefined;
        }
        if (!videoDurationSeconds) {
            return undefined;
        }
        return thumbnail.thumbnailOffsetTimeInMs / 1000 / videoDurationSeconds;
    });

    private _videoUrl = computed(() => {
        const media = this.media();
        return media?.videoUrl;
    });

    private _videoDurationSeconds = signal<number | null>(null);

    readonly isTiktokChecked = computed(() => {
        const post = this._upsertSocialPostContext.upsertSocialPostState.post();
        return post?.platformKeys?.includes(PlatformKey.TIKTOK);
    });

    readonly isInstagramChecked = computed(() => {
        const post = this._upsertSocialPostContext.upsertSocialPostState.post();
        return post?.platformKeys?.includes(PlatformKey.INSTAGRAM);
    });
    readonly hasThumbnailFromMedia = computed(() => !!this.reelThumbnailFromMedia());
    readonly hasThumbnailFromFrame = computed(() => !!this.reelThumbnailFromFrame());

    readonly showSelectThumbnailFromMediaTemplate = computed(() => this.isInstagramChecked() && !this.hasThumbnailFromMedia());
    readonly showDeleteSelectedThumbnailFromMediaTemplate = computed(() => this.isInstagramChecked() && this.hasThumbnailFromMedia());
    readonly showSelectThumbnailFromFrameTemplate = computed(
        () => this.isTiktokChecked() || (this.isInstagramChecked() && !this.hasThumbnailFromMedia())
    );

    readonly SvgIcon = SvgIcon;
    readonly PlatformKey = PlatformKey;

    readonly IMAGE_MIME_TYPES = [MimeType.IMAGE_PNG, MimeType.IMAGE_JPEG, MimeType.IMAGE_HEIC, MimeType.IMAGE_HEIF];

    constructor() {
        this._mediaUploaderService.setPublicationType(PublicationType.REEL);

        effect(() => {
            const media = this.media();
            if (media?.duration) {
                this._videoDurationSeconds.set(media.duration);
            }
        });

        // Generate the pictures of the timeline of the slider the after the component is created
        effect((onCleanup) => {
            let destroyed = false;

            const videoUrl = this._videoUrl();
            if (!videoUrl) {
                return;
            }

            const timelinePreviewFrames256hUrls = this.media()?.timelinePreviewFrames256hUrls;
            if (timelinePreviewFrames256hUrls && timelinePreviewFrames256hUrls.length > 0) {
                this.timelinePreviewUrls.set(timelinePreviewFrames256hUrls);
                return;
            }

            this.timelineLoadingProgress.set(0);

            extractSpacedThumbnails(videoUrl, 5, 200).then(({ videoDurationInSeconds, thumbnails }) => {
                if (destroyed) {
                    return;
                }
                this._videoDurationSeconds.set(videoDurationInSeconds);
                this.timelineLoadingProgress.set(undefined);
                this.timelinePreviewUrls.set(thumbnails);
            });

            onCleanup(() => {
                destroyed = true;
                for (const url of this.timelinePreviewUrls()) {
                    window.URL.revokeObjectURL(url);
                }
                this.timelinePreviewUrls.set([]);
            });
        });

        effect(() => {
            const sliderThumbnailUrl = this.sliderThumbnailUrl();
            if (sliderThumbnailUrl) {
                this.previousSliderThumbnailUrl.set(sliderThumbnailUrl);
            }
        });

        effect(() => {
            const uploadingMediaCount = this._mediaUploaderService.mediaCount();
            this._upsertSocialPostContext.trackUploadMediaCount('ReelThumbnailComponent', uploadingMediaCount);
        });
    }

    onPositionChange(position: number): void {
        const videoDurationSeconds = this._videoDurationSeconds();
        if (!videoDurationSeconds) {
            return;
        }
        this._upsertSocialPostContext.updateReelThumbnailFromFrame({
            thumbnailOffsetTimeInMs: position * videoDurationSeconds * 1000,
        });
    }

    async chooseCustomReelThumbnailFromFile(event: Event): Promise<void> {
        const target = event.target as HTMLInputElement;
        const files = Array.from(target.files as FileList);
        if (files.length === 1) {
            const media = await this._mediaUploaderService.uploadFromFile(files[0]);
            if (media) {
                this._upsertSocialPostContext.updateReelThumbnailFromMedia(media);
            }
        }
    }

    async chooseCustomReelThumbnailFromGallery(): Promise<void> {
        const medias: Media[] | false = await lastValueFrom(
            this._customDialogService
                .open(MediaPickerModalComponent, {
                    width: '600px',
                    data: {
                        restaurant: this._restaurantsService.currentRestaurant,
                        multi: false,
                        filter: MediaPickerFilter.ONLY_IMAGE,
                        selectedMedias: [],
                        maxMedia: 1,
                    },
                })
                .afterClosed()
        );
        if (!medias || !medias.length) {
            return;
        }
        const newMedia = await this._mediaUploaderService.uploadFromGalleryMediaId(medias[0].id);
        if (newMedia) {
            this._upsertSocialPostContext.updateReelThumbnailFromMedia(newMedia);
        }
    }

    onDeleteThumbnailFromMedia(): void {
        this._upsertSocialPostContext.updateReelThumbnailFromMedia(undefined);
    }
}
