import { Injectable } from '@angular/core';
import { omitBy, pickBy } from 'lodash';
import { DateTime } from 'luxon';
import { catchError, forkJoin, map, Observable, of } from 'rxjs';

import { GetStoredInsightsAggregatedRequestInputBodyDto, StoredInsightsAggregatedResponseDto } from '@malou-io/package-dto';
import { AggregationType, GeoSamplePlatform, InsightsChart, PlatformKey } from '@malou-io/package-utils';

import { KeywordsService } from ':core/services/keywords.service';
import { GmbAggregatedInsightsChartData } from ':modules/aggregated-statistics/seo/models/gmb-aggregated-insights-chart-data';
import { InsightsService } from ':modules/statistics/insights.service';
import { KeywordSearchImpressionsService } from ':modules/statistics/seo/keyword-search-impressions/keyword-search-impressions.service';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { getDayMonthYearFromDate } from ':shared/helpers';
import { GMB_METRICS } from ':shared/interfaces';
import { LightRestaurant } from ':shared/models';
import {
    AggregatedSummarySectionsData,
    AggregatedSummarySectionsDataFilters,
} from ':shared/services/csv-services/aggregated-insights/aggregated-summary/aggregated-summary.interface';
import { formatDataWithRestaurantInformation } from ':shared/services/csv-services/shared/summary/utils';

@Injectable({ providedIn: 'root' })
export class AggregatedSummaryCsvInsightsSeoSectionService {
    constructor(
        private readonly _insightsService: InsightsService,
        private readonly _keywordsService: KeywordsService,
        private readonly _keywordSearchImpressionsService: KeywordSearchImpressionsService
    ) {}

    execute(
        filters: AggregatedSummarySectionsDataFilters['seo']
    ): Observable<{ current: AggregatedSummarySectionsData['seo'] | null; previous: AggregatedSummarySectionsData['seo'] | null }> {
        const { startMonthYear, endMonthYear, comparisonPeriodEndDate, restaurants, comparisonPeriodStartDate, comparisonPeriod } = filters;
        const restaurantIds = restaurants.map((r) => r.id);
        const requestBody: GetStoredInsightsAggregatedRequestInputBodyDto = {
            restaurantIds,
            platformKeys: [PlatformKey.GMB],
            startDate: filters.startDate.toISOString(),
            endDate: filters.endDate.toISOString(),
            metrics: GMB_METRICS.map((m) => m.metric),
            aggregationType: AggregationType.TOTAL,
        };

        // Recreate the dates for some calls
        const startDateLuxon = DateTime.fromObject(filters.startMonthYear);
        const endDateLuxon = DateTime.fromObject(filters.endMonthYear);

        const startDate = startDateLuxon.startOf('month').toJSDate();
        const endDate = endDateLuxon.endOf('month').toJSDate();

        const previousStartMonthYear = getDayMonthYearFromDate(comparisonPeriodStartDate);
        const previousEndMonthYear = getDayMonthYearFromDate(comparisonPeriodEndDate);

        return forkJoin([
            // Actions and impressions
            this._insightsService.getStoredInsightsAggregated(requestBody),
            this._insightsService.getStoredInsightsAggregated({ ...requestBody, comparisonPeriod }),
            // Keywords
            this._keywordsService.getAggregatedKeywordRankings({
                restaurantIds,
                startDate,
                endDate,
                platformKey: GeoSamplePlatform.GMAPS,
            }),
            this._keywordsService.getAggregatedKeywordRankings({
                restaurantIds,
                startDate,
                endDate,
                platformKey: GeoSamplePlatform.GMAPS,
                comparisonPeriod,
            }),
            // Keywords impressions
            this._keywordSearchImpressionsService.getKeywordSearchImpressionsAggregatedInsights({
                monthYearPeriod: {
                    endMonthYear,
                    startMonthYear,
                },
                restaurantIds,
                aggregationType: AggregationType.TOTAL,
            }),
            this._keywordSearchImpressionsService.getKeywordSearchImpressionsAggregatedInsights({
                monthYearPeriod: {
                    endMonthYear: previousEndMonthYear,
                    startMonthYear: previousStartMonthYear,
                },
                restaurantIds,
                aggregationType: AggregationType.TOTAL,
                comparisonPeriod,
            }),
        ]).pipe(
            map(
                ([
                    currentActionInsights,
                    previousActionInsights,
                    currentKeywordsInsights,
                    previousKeywordsInsights,
                    currentSearchImpressions,
                    previousSearchImpressions,
                ]) => {
                    const seoSectionData = this._getActionsAndImpressionsData({ data: currentActionInsights, restaurants });
                    const previousSeoSectionData = this._getActionsAndImpressionsData({ data: previousActionInsights, restaurants });

                    if (!seoSectionData) {
                        return {
                            current: null,
                            previous: null,
                        };
                    }

                    seoSectionData.keywordsInTop20Count = currentKeywordsInsights.restaurants.reduce((acc, r) => acc + r.rankings.top20, 0);

                    const totalSearch = Object.values(currentSearchImpressions).reduce(
                        (acc: { branding: number; discovery: number }, curr) => ({
                            branding: (acc.branding ?? 0) + (curr.total?.branding ?? 0),
                            discovery: (acc.discovery ?? 0) + (curr.total?.discovery ?? 0),
                        }),
                        { branding: 0, discovery: 0 }
                    );

                    seoSectionData.totalNotorietySearchCount = totalSearch.branding;
                    seoSectionData.totalDiscoverySearchCount = totalSearch.discovery;

                    if (previousSeoSectionData) {
                        previousSeoSectionData.keywordsInTop20Count = previousKeywordsInsights.restaurants.reduce(
                            (acc, r) => acc + r.rankings.top20,
                            0
                        );

                        const previousTotalSearch = Object.values(previousSearchImpressions).reduce(
                            (acc: { branding: number; discovery: number }, curr) => ({
                                branding: (acc.branding ?? 0) + (curr.total?.branding ?? 0),
                                discovery: (acc.discovery ?? 0) + (curr.total?.discovery ?? 0),
                            }),
                            { branding: 0, discovery: 0 }
                        );
                        previousSeoSectionData.totalNotorietySearchCount = previousTotalSearch.branding;
                        previousSeoSectionData.totalDiscoverySearchCount = previousTotalSearch.discovery;
                    }

                    return {
                        current: seoSectionData,
                        previous: previousSeoSectionData || null,
                    };
                }
            ),
            catchError(() => of({ current: null, previous: null }))
        );
    }

    private _getActionsAndImpressionsData({
        data,
        restaurants,
    }: {
        data: StoredInsightsAggregatedResponseDto;
        restaurants: LightRestaurant[];
    }): AggregatedSummarySectionsData['seo'] {
        const currentInsightsInError = pickBy(data, (value) => value?.[PlatformKey.GMB]?.hasData === false);
        const restaurantIdsInError = Object.keys(currentInsightsInError);
        const restaurantsToConsider = restaurants.filter((restaurant) => !restaurantIdsInError.includes(restaurant.id));
        const insights = omitBy(data, (_value, key) => restaurantIdsInError.includes(key));

        const actionsData = new GmbAggregatedInsightsChartData({
            chart: InsightsChart.AGGREGATED_ACTIONS,
            data: insights as StoredInsightsAggregatedResponseDto,
            restaurants: restaurantsToConsider,
            sortBy: ChartSortBy.DESC,
        });

        const totalActionsByRestaurant = actionsData.restaurants.map((restaurant, index) => {
            const bookingClicks = actionsData.bookingClicks[index] ?? 0;
            const drivingClicks = actionsData.drivingClicks[index] ?? 0;
            const foodOrderClicks = actionsData.foodOrderClicks[index] ?? 0;
            const menuClicks = actionsData.menuClicks[index] ?? 0;
            const phoneClicks = actionsData.phoneClicks[index] ?? 0;
            const websiteClicks = actionsData.websiteClicks[index] ?? 0;
            const totalActions = bookingClicks + drivingClicks + foodOrderClicks + menuClicks + phoneClicks + websiteClicks;
            return {
                data: formatDataWithRestaurantInformation({ restaurant, data: totalActions }),
                totalActions,
            };
        });

        const totalAppearancesByRestaurant = actionsData.restaurants.map((restaurant, index) => {
            const impressionsMap = actionsData.impressionsMaps[index] ?? 0;
            const impressionsSearch = actionsData.impressionsSearch[index] ?? 0;
            const totalImpressions = impressionsMap + impressionsSearch;
            return {
                data: formatDataWithRestaurantInformation({ restaurant, data: totalImpressions }),
                totalImpressions,
            };
        });

        const sortedByTotalActions = totalActionsByRestaurant.sort((a, b) => b.totalActions - a.totalActions);
        const sortedByTotalAppearances = totalAppearancesByRestaurant.sort((a, b) => b.totalImpressions - a.totalImpressions);

        return {
            actions: {
                totalActions: actionsData.totalActions,
                conversionRate: actionsData.ratioActionsOverImpressions,
                totalCallCount: actionsData.phoneClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalDirectionCount: actionsData.drivingClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalWebsiteVisitCount: actionsData.websiteClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalMenuClickCount: actionsData.menuClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalBookingClickCount: actionsData.bookingClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
                totalOrderClickCount: actionsData.foodOrderClicks.reduce((acc, curr) => (acc ?? 0) + (curr ?? 0), 0),
            },
            impressions: {
                totalAppearanceMaps: actionsData.totalImpressionsMaps,
                totalAppearanceSearch: actionsData.totalImpressionsSearch,
                totalAppearance: actionsData.totalImpressions,
            },
            keywordsInTop20Count: null,
            totalNotorietySearchCount: null,
            totalDiscoverySearchCount: null,
            flop3Actions: sortedByTotalActions.slice(-3).map((r) => r.data),
            top3Actions: sortedByTotalActions.slice(0, 3).map((r) => r.data),
            flop3Appearances: sortedByTotalAppearances.slice(-3).map((r) => r.data),
            top3Appearances: sortedByTotalAppearances.slice(0, 3).map((r) => r.data),
            googlePostCount: null,
        };
    }
}
