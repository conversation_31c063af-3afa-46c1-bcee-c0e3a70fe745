import { MalouComparisonPeriod, MonthAndYear, PlatformKey } from '@malou-io/package-utils';

import { LightNfc, LightRestaurant } from ':shared/models';

interface AggregatedBasicFilters {
    restaurants: LightRestaurant[];
    startDate: Date;
    endDate: Date;
    comparisonPeriodStartDate: Date;
    comparisonPeriodEndDate: Date;
    comparisonPeriod: MalouComparisonPeriod;
}
export interface AggregatedSummarySectionsDataFilters {
    restaurantNames: string[];
    startDate: Date;
    endDate: Date;
    comparisonPeriodStartDate: Date;
    comparisonPeriodEndDate: Date;
    seo: AggregatedBasicFilters & {
        startMonthYear: MonthAndYear;
        endMonthYear: MonthAndYear;
    };
    eReputation: AggregatedBasicFilters & {
        platformKeys: PlatformKey[];
    };
    booster: AggregatedBasicFilters & {
        nfcs: LightNfc[];
    };
}
export interface AggregatedSummarySectionsData {
    seo: {
        impressions: {
            totalAppearanceMaps: number | null;
            totalAppearanceSearch: number | null;
            totalAppearance: number | null;
        };
        actions: {
            totalActions: number | null;
            conversionRate: number | null;
            totalCallCount: number | null;
            totalDirectionCount: number | null;
            totalWebsiteVisitCount: number | null;
            totalMenuClickCount: number | null;
            totalBookingClickCount: number | null;
            totalOrderClickCount: number | null;
        };
        totalNotorietySearchCount: number | null;
        totalDiscoverySearchCount: number | null;
        keywordsInTop20Count: number | null;
        googlePostCount: number | null;
        top3Appearances: string[] | null;
        flop3Appearances: string[] | null;
        top3Actions: string[] | null;
        flop3Actions: string[];
    } | null;
    eReputation: {
        reviews: {
            totalCount: number | null;
            averageRating: number | null;
        };
        sentimentsPercentage: {
            positive: number | null;
            negative: number | null;
        };
        notes: {
            platform: PlatformKey;
            note: number | null;
        }[];
        top3GoogleRating: string[] | null;
        flop3GoogleRating: string[] | null;
        top3ReviewCount: string[] | null;
        flop3ReviewCount: string[] | null;
    } | null;
    booster: {
        totalTotemScanCount: number | null;
        totalWofScanCount: number | null;
        gainedPrivateReviewCount: number | null;
        gainedPublicReviewCount: number | null;
        winnerCount: number | null;
        giftCount: number | null;
        top3TotemScanCount: string[] | null;
        flop3TotemScanCount: string[] | null;
        top3WofScanCount: string[] | null;
        flop3WofScanCount: string[] | null;
    } | null;
}

export interface AggregatedSummaryCsvData {
    current: AggregatedSummarySectionsData;
    previous: AggregatedSummarySectionsData;
    startDate: string;
    endDate: string;
    previousStartDate: string;
    previousEndDate: string;
    restaurantNames: string[];
}

export type AggregatedSummarySectionsTranslationKeys = keyof AggregatedSummarySectionsData;
